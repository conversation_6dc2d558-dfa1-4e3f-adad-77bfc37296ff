# Fulfillmen Shop 开发

## 简介
Fulfillmen Shop是一款高效的采购业务系统，专为Fulfillmen WMS系统用户量身打造，可实现私有化部署，作为Fulfillmen WMS系统功能的一个重要扩展。

该系统旨在简化Fulfillmen WMS用户的商品采购过程，通过与多个平台的无缝对接，强化了其商品采购能力。Fulfillmen Shop不仅支持从各大销售平台采购商品，还实现了订单与库存的实时同步，确保信息的准确性和及时性。

系统特设前台处理页面，用户界面友好，操作简便，使得用户能够轻松管理采购订单。后台功能则包括商品管理和订单处理等多项功能，有效支持企业的日常运营需求。

## 架构设计
... 架构图

## 技术选型

### 后端技术栈
- Java 17 
- SpringBoot 3 
- Redis 分布式缓存
- Caffeine 本地缓存 
- Jetcache (整合 Caffeine + Redis 缓存)
- MyBatis Plus ORM 
- Liquibase (数据库版本管理)

### 前端技术栈
- Vue 3
- TypeScript
- Vite
- Element Plus
- Pinia
- Vue Router
- Axios
- VueUse

## 工程结构


### 后端工程结构
```bash
fulfillmen-shop/
├── fulfillmen-shop-common/ # 公共类库
│ ├── config/ # 公共配置
│ ├── exception/ # 异常处理
│ └── utils/ # 工具类
├── fulfillmen-shop-model/ # 数据模型
│ ├── entity/ # 数据库实体
│ ├── dto/ # 数据传输对象
│ └── vo/ # 视图对象
├── fulfillmen-shop-dao/ # 数据访问层
│ ├── mapper/ # MyBatis mapper
│ └── repository/ # 数据仓库
├── fulfillmen-shop-manager/ # 通用业务管理
│ ├── cache/ # 缓存管理
│ └── integration/ # 第三方集成
├── fulfillmen-shop-service/ # 业务服务层
│ ├── product/ # 商品服务
│ └── order/ # 订单服务
├── fulfillmen-shop-openapi/ # 开放接口层
│ └── controller/ # API控制器
└── fulfillmen-shop-portal/ # Web应用入口
├── config/ # 应用配置
└── resources/ # 资源文件
```
### 前端工程结构
```bash
fulfillmen-shop-frontend/
├── src/
│ ├── api/ # API 接口定义
│ │ ├── product.ts # 商品相关接口
│ │ ├── order.ts # 订单相关接口
│ │ └── types.ts # API 类型定义
│ ├── assets/ # 静态资源
│ ├── components/ # 公共组件
│ │ ├── base/ # 基础组件
│ │ └── business/ # 业务组件
│ ├── composables/ # 组合式函数
│ ├── layouts/ # 布局组件
│ ├── router/ # 路由配置
│ ├── stores/ # Pinia 状态管理
│ │ ├── modules/
│ │ └── index.ts
│ ├── types/ # 类型定义
│ ├── utils/ # 工具函数
│ │ ├── request.ts # Axios 封装
│ │ └── auth.ts # 认证相关
│ └── views/ # 页面组件
│ ├── product/ # 商品相关页面
│ └── order/ # 订单相关页面
```

## 前后端交互设计

### 1. 接口规范
- 采用 RESTful API 设计规范
- 统一响应格式：
    ```typescript
    typescript interface ApiResponse<T> {
        code: number; // 状态码
        message: string; // 提示信息
        data: T; // 响应数据
        timestamp: number; // 时间戳
    }
    ```

### 2. 认证交互
- 采用 JWT Token 认证机制
- Token 存储在 localStorage，请求时通过 Authorization 头部传递
- 支持 Token 自动刷新机制

### 3. 数据交互流程
1. 前端通过 API 模块统一管理接口请求
2. 使用 Axios 拦截器统一处理请求/响应
3. 后端统一异常处理，返回标准响应格式
4. 前端统一处理响应数据，处理错误信息

### 4. 代码示例
#### 前端 API 定义
```typescript
// api/product.ts
import request from '@/utils/request'
import type { Product } from '@/types'

export const getProductList = (params: ProductQueryParams) => {
    return request.get<ApiResponse<Product[]>>('/api/products', { params })
}
```

#### 后端接口实现

```java
@RestController
@RequestMapping("/api/products")
public class ProductController {
    @GetMapping
    public ApiResponse<List<ProductVO>> getProducts(ProductQueryDTO query) {
        return ApiResponse.success(productService.queryProducts(query));
    }
}
```

### 5. 缓存策略
- 前端：
  - 使用 Pinia 进行状态管理
  - localStorage 存储用户信息
  - 路由组件缓存 (keep-alive)
- 后端：
  - Caffeine 本地缓存
  - Redis 分布式缓存
  - Jetcache 统一缓存管理

### 6. 错误处理
- 前端统一错误处理
- 后端全局异常处理
- 友好的错误提示
- 详细的日志记录

## 开发流程
1. 后端开发接口
2. 使用 Swagger 生成接口文档
3. 前端根据接口文档开发对应功能
4. 本地联调
5. 提交代码，执行 CI/CD 流程

## 部署说明
虽然采用前后端分离开发，但最终打包为单体应用：
1. 前端代码打包到 `fulfillmen-shop-portal/src/main/resources/static`
2. 后端打包时会将前端资源一起打包到 jar 文件
3. 部署时只需要部署一个 jar 包即可

## 注意事项
1. 代码提交前需要执行代码格式化
2. 遵循统一的编码规范
3. 及时更新接口文档
4. 注意代码安全，避免敏感信息泄露

## 发布管理
- 系统版本采用 Git Release 方式发布
- 使用 CI + CD Docker 提供持续集成能力
- **TODO: 需要考虑代码混淆加密问题，加强源码防止被反编译**

## 购物车规格属性JSON处理

### AttrJson类结构

`AttrJson`类是用于存储商品规格属性的数据结构，支持多语言。主要包含两个字段:

- `attr`: 中文规格属性集合
- `attrTransEn`: 英文规格属性集合

每个属性包含键值对:
- `attrKey`: 属性名称 (如"尺寸", "颜色")
- `attrValue`: 属性值 (如"XL", "红色")

### 自定义TypeHandler实现

`AttrJsonTypeHandler`是一个自定义的MyBatis类型处理器，用于将JSON字符串与Java对象之间进行转换:

1. 继承自`AbstractJsonTypeHandler<AttrJson>`
2. 使用Jackson库进行序列化和反序列化
3. 处理SQL中的VARCHAR类型与Java中的AttrJson对象之间的映射

### 使用方法

在实体类中使用:

```java
@TableField(value = "specs", typeHandler = AttrJsonTypeHandler.class)
private AttrJson specs;
```

在Mapper XML中使用:

```xml

<result column="specs" jdbcType="VARCHAR" property="specs"
  typeHandler="json.com.fulfillmen.shop.po.AttrJsonTypeHandler"/>
```

### 数据示例

中文属性:
```
{
  "attr": [
    {"attrKey": "尺寸", "attrValue": "XL"},
    {"attrKey": "颜色", "attrValue": "红色"}
  ]
}
```

多语言属性:
```
{
  "attr": [
    {"attrKey": "尺寸", "attrValue": "XL"},
    {"attrKey": "颜色", "attrValue": "红色"}
  ],
  "attrTransEn": [
    {"attrKey": "size", "attrValue": "XL"},
    {"attrKey": "color", "attrValue": "red"}
  ]
}
```
