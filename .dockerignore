# 排除不需要的文件
.git
.github
.gitignore
README.md
LICENSE

# 排除构建产物和临时文件
**/target/
!**/target/*.jar
**/*.class
**/node_modules/
**/.DS_Store
**/*.log
**/logs/
**/tmp/

# 排除IDE相关文件
.idea
*.iml
.vscode
.project
.classpath
.flattened-pom.xml
.settings/

# 排除测试文件
**/src/test/
**/*Test.java

# 排除文档目录
/docs/
/docker/
/logs/
/kubernetes/
/documentation/

# Dockerfile本身不需要被复制
Dockerfile
Dockerfile_aliyun
.dockerignore
docker-compose*.yml!/fulfillmen-shop-development-guide.md

!/fulfillmen-workspace-quick-preview.mdc
/.cursorrules

/.editorconfig

/.gitattributes

/build-image.sh

/CLAUDE.md

/docker-compose.yml

/fulfillmen-shop-development-guide.md

/fulfillmen-workspace-quick-preview.mdc
/lombok.config

/office-dev.session.sql

/pom.xml

/readme.md

/sealo MySQL 8.x.session.sql

/TestUrlEncoding.class

/VerifyCorrectSignature.class
