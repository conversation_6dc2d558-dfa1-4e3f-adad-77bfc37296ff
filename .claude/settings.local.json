{"permissions": {"allow": ["Bash(find:*)", "Bash(rg:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mvnd clean:*)", "Bash(mvnd spring-boot:run:*)", "Bash(pnpm run:*)", "<PERSON><PERSON>(timeout 10s pnpm run dev)", "Bash(node:*)", "Bash(npx tsc:*)", "Bash(mvnd compile:*)", "<PERSON><PERSON>(mvnd test:*)", "Bash(mvnd surefire:test:*)", "<PERSON><PERSON>(mvnd help:*)", "Bash(tree:*)", "mcp__zen__codereview", "Bash(git add:*)", "Bash(git commit:*)", "<PERSON><PERSON>(javac:*)", "<PERSON><PERSON>(mvn clean:*)", "Bash(/bashes)", "Ba<PERSON>(mvnd spotless:apply:*)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(mvnd spotless:check:*)", "Bash(grep:*)", "Bash(xxd:*)", "Bash(__NEW_LINE__ cp src/test/java/com/fulfillmen/shop/manager/product/service/IProductFacadeServiceTest.java.bak src/test/java/com/fulfillmen/shop/manager/product/service/IProductFacadeServiceTest.java)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(timeout:*)"], "deny": []}}