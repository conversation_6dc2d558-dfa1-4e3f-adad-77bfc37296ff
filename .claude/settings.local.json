{"permissions": {"allow": ["Bash(find:*)", "mcp__ide__getDiagnostics", "<PERSON><PERSON>(mvnd clean:*)", "Bash(mvnd compile:*)", "Bash(awk:*)", "Bash(grep:*)", "<PERSON><PERSON>(mvnd spotless:check:*)", "Bash(rm:*)", "Ba<PERSON>(mvnd spotless:apply:*)", "Bash(mvnd:*)", "Bash(cp:*)", "<PERSON><PERSON>(chmod:*)", "Bash(/Users/<USER>/work/fulfillmen/fulfillmen-shop/test_parse.sh)", "<PERSON><PERSON>(javac:*)", "mcp__zen__chat", "Bash(git add:*)"], "deny": [], "ask": []}}