2025-09-10 14:42:15 INFO  [background-preinit] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-09-10 14:42:16 WARN  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanDefinitionStoreException: Failed to parse configuration class [com.fulfillmen.shop.BootstrapApplication]
2025-09-10 14:42:16 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-09-10 14:42:16 ERROR [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.springframework.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanDefinitionStoreException: Failed to parse configuration class [com.fulfillmen.shop.BootstrapApplication]
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:187)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.processConfigBeanDefinitions(ConfigurationClassPostProcessor.java:417)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.postProcessBeanDefinitionRegistry(ConfigurationClassPostProcessor.java:290)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanDefinitionRegistryPostProcessors(PostProcessorRegistrationDelegate.java:349)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:118)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:789)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:607)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:755)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1364)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1353)
	at com.fulfillmen.shop.BootstrapApplication.main(BootstrapApplication.java:45)
Caused by: org.springframework.context.annotation.ConflictingBeanDefinitionException: Annotation-specified bean name 'productSyncServiceImpl' for bean class [com.fulfillmen.shop.manager.service.impl.ProductSyncServiceImpl] conflicts with existing, non-compatible bean definition of same name and class [com.fulfillmen.shop.manager.product.service.ProductSyncServiceImpl]
	at org.springframework.context.annotation.ClassPathBeanDefinitionScanner.checkCandidate(ClassPathBeanDefinitionScanner.java:361)
	at org.springframework.context.annotation.ClassPathBeanDefinitionScanner.doScan(ClassPathBeanDefinitionScanner.java:288)
	at org.springframework.context.annotation.ComponentScanAnnotationParser.parse(ComponentScanAnnotationParser.java:128)
	at org.springframework.context.annotation.ConfigurationClassParser.doProcessConfigurationClass(ConfigurationClassParser.java:320)
	at org.springframework.context.annotation.ConfigurationClassParser.processConfigurationClass(ConfigurationClassParser.java:260)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:196)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:165)
	... 13 common frames omitted
2025-09-10 14:43:47 INFO  [background-preinit] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-09-10 14:43:47 WARN  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanDefinitionStoreException: Failed to parse configuration class [com.fulfillmen.shop.BootstrapApplication]
2025-09-10 14:43:47 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-09-10 14:43:47 ERROR [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.springframework.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanDefinitionStoreException: Failed to parse configuration class [com.fulfillmen.shop.BootstrapApplication]
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:187)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.processConfigBeanDefinitions(ConfigurationClassPostProcessor.java:417)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.postProcessBeanDefinitionRegistry(ConfigurationClassPostProcessor.java:290)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanDefinitionRegistryPostProcessors(PostProcessorRegistrationDelegate.java:349)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:118)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:789)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:607)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:755)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1364)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1353)
	at com.fulfillmen.shop.BootstrapApplication.main(BootstrapApplication.java:45)
Caused by: org.springframework.context.annotation.ConflictingBeanDefinitionException: Annotation-specified bean name 'productSyncServiceImpl' for bean class [com.fulfillmen.shop.manager.service.impl.ProductSyncServiceImpl] conflicts with existing, non-compatible bean definition of same name and class [com.fulfillmen.shop.manager.product.service.ProductSyncServiceImpl]
	at org.springframework.context.annotation.ClassPathBeanDefinitionScanner.checkCandidate(ClassPathBeanDefinitionScanner.java:361)
	at org.springframework.context.annotation.ClassPathBeanDefinitionScanner.doScan(ClassPathBeanDefinitionScanner.java:288)
	at org.springframework.context.annotation.ComponentScanAnnotationParser.parse(ComponentScanAnnotationParser.java:128)
	at org.springframework.context.annotation.ConfigurationClassParser.doProcessConfigurationClass(ConfigurationClassParser.java:320)
	at org.springframework.context.annotation.ConfigurationClassParser.processConfigurationClass(ConfigurationClassParser.java:260)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:196)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:165)
	... 13 common frames omitted
2025-09-10 17:02:09 INFO  [background-preinit] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-09-10 17:02:10 WARN  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanDefinitionStoreException: Failed to parse configuration class [com.fulfillmen.shop.BootstrapApplication]
2025-09-10 17:02:10 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-09-10 17:02:10 ERROR [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.springframework.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanDefinitionStoreException: Failed to parse configuration class [com.fulfillmen.shop.BootstrapApplication]
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:187)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.processConfigBeanDefinitions(ConfigurationClassPostProcessor.java:417)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.postProcessBeanDefinitionRegistry(ConfigurationClassPostProcessor.java:290)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanDefinitionRegistryPostProcessors(PostProcessorRegistrationDelegate.java:349)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:118)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:789)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:607)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:755)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1364)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1353)
	at com.fulfillmen.shop.BootstrapApplication.main(BootstrapApplication.java:45)
Caused by: org.springframework.context.annotation.ConflictingBeanDefinitionException: Annotation-specified bean name 'productSyncServiceImpl' for bean class [com.fulfillmen.shop.manager.service.impl.ProductSyncServiceImpl] conflicts with existing, non-compatible bean definition of same name and class [com.fulfillmen.shop.manager.product.service.ProductSyncServiceImpl]
	at org.springframework.context.annotation.ClassPathBeanDefinitionScanner.checkCandidate(ClassPathBeanDefinitionScanner.java:361)
	at org.springframework.context.annotation.ClassPathBeanDefinitionScanner.doScan(ClassPathBeanDefinitionScanner.java:288)
	at org.springframework.context.annotation.ComponentScanAnnotationParser.parse(ComponentScanAnnotationParser.java:128)
	at org.springframework.context.annotation.ConfigurationClassParser.doProcessConfigurationClass(ConfigurationClassParser.java:320)
	at org.springframework.context.annotation.ConfigurationClassParser.processConfigurationClass(ConfigurationClassParser.java:260)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:196)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:165)
	... 13 common frames omitted
2025-09-10 17:06:47 INFO  [background-preinit] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-09-10 17:06:49 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-10 17:06:49 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-10 17:06:49 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 232 ms. Found 0 Redis repository interfaces.
2025-09-10 17:06:50 WARN  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-09-10 17:06:50 WARN  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-09-10 17:06:51 WARN  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-09-10 17:06:51 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-09-10 17:06:51 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3444 ms
2025-09-10 17:06:51 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= caffeine
2025-09-10 17:06:51 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redisson
2025-09-10 17:06:51 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.redisson.Version - Redisson 3.45.1
2025-09-10 17:06:52 INFO  [redisson-netty-1-7] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.redisson.connection.ConnectionsHolder - 1 connections initialized for dbconn.sealoshzh.site/************:47683
2025-09-10 17:06:53 INFO  [redisson-netty-1-20] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.redisson.connection.ConnectionsHolder - 24 connections initialized for dbconn.sealoshzh.site/************:47683
2025-09-10 17:06:55 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-09-10 17:06:55 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] c.b.m.e.spring.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@69e49a81
2025-09-10 17:06:55 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Distribute Remote instanceId:[InstanceId{instanceId=**************:97887, stable=false}] - machineBit:[20] @ namespace:[fulfillmen-shop].
2025-09-10 17:06:55 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Distribute Remote machineState:[MachineState{machineId=15, lastTimeStamp=1757495215603}] - instanceId:[InstanceId{instanceId=**************:97887, stable=false}] - machineBit:[20] @ namespace:[fulfillmen-shop].
2025-09-10 17:06:55 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no].
2025-09-10 17:06:55 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no] is bound to thread:[DefaultPrefetchWorker-1].
2025-09-10 17:06:55 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-09-10 17:06:55 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Submit [fulfillmen-shop.order_no] jobSize:[0].
2025-09-10 17:06:55 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no].
2025-09-10 17:06:55 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no] is bound to thread:[DefaultPrefetchWorker-2].
2025-09-10 17:06:55 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no] is bound to thread:[DefaultPrefetchWorker-2] start.
2025-09-10 17:06:55 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Submit [fulfillmen-shop.user_no] jobSize:[0].
2025-09-10 17:06:56 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.undertow - starting server: Undertow - 2.3.18.Final
2025-09-10 17:06:56 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.xnio - XNIO version 3.8.16.Final
2025-09-10 17:06:56 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-09-10 17:06:56 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-09-10 17:06:56 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.undertow - stopping server: Undertow - 2.3.18.Final
2025-09-10 17:06:56 WARN  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-09-10 17:06:56 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-09-10 17:06:56 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-10 17:06:56 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-10 17:06:56 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-10 17:06:56 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-10 17:06:56 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-10 17:06:56 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-10 17:06:56 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-10 17:06:56 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-10 17:06:56 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-10 17:06:56 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-10 17:06:56 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-10 17:06:56 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-10 17:06:56 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-09-10 17:06:56 ERROR [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.b.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8080 was already in use.

Action:

Identify and stop the process that's listening on port 8080 or configure this application to listen on another port.

2025-09-10 17:06:56 INFO  [Thread-3] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Close gracefully!
2025-09-10 17:06:56 INFO  [Thread-3] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-09-10 17:06:56 INFO  [Thread-2] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Close gracefully!
2025-09-10 17:06:56 INFO  [Thread-2] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-09-10 17:07:12 INFO  [background-preinit] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-09-10 17:07:14 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-10 17:07:14 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-10 17:07:14 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 108 ms. Found 0 Redis repository interfaces.
2025-09-10 17:07:15 WARN  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-09-10 17:07:15 WARN  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-09-10 17:07:16 WARN  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-09-10 17:07:16 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-09-10 17:07:16 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3894 ms
2025-09-10 17:07:17 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= caffeine
2025-09-10 17:07:17 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redisson
2025-09-10 17:07:17 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.redisson.Version - Redisson 3.45.1
2025-09-10 17:07:18 INFO  [redisson-netty-1-7] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.redisson.connection.ConnectionsHolder - 1 connections initialized for dbconn.sealoshzh.site/************:47683
2025-09-10 17:07:19 INFO  [redisson-netty-1-20] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.redisson.connection.ConnectionsHolder - 24 connections initialized for dbconn.sealoshzh.site/************:47683
2025-09-10 17:07:21 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-09-10 17:07:21 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] c.b.m.e.spring.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@3f93e4a8
2025-09-10 17:07:21 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Distribute Remote instanceId:[InstanceId{instanceId=**************:98455, stable=false}] - machineBit:[20] @ namespace:[fulfillmen-shop].
2025-09-10 17:07:21 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Distribute Remote machineState:[MachineState{machineId=16, lastTimeStamp=1757495241445}] - instanceId:[InstanceId{instanceId=**************:98455, stable=false}] - machineBit:[20] @ namespace:[fulfillmen-shop].
2025-09-10 17:07:21 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no].
2025-09-10 17:07:21 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no] is bound to thread:[DefaultPrefetchWorker-1].
2025-09-10 17:07:21 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-09-10 17:07:21 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Submit [fulfillmen-shop.order_no] jobSize:[0].
2025-09-10 17:07:21 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no].
2025-09-10 17:07:21 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no] is bound to thread:[DefaultPrefetchWorker-2].
2025-09-10 17:07:21 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no] is bound to thread:[DefaultPrefetchWorker-2] start.
2025-09-10 17:07:21 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Submit [fulfillmen-shop.user_no] jobSize:[0].
2025-09-10 17:07:22 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.undertow - starting server: Undertow - 2.3.18.Final
2025-09-10 17:07:22 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.xnio - XNIO version 3.8.16.Final
2025-09-10 17:07:22 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-09-10 17:07:22 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-09-10 17:07:22 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.undertow - stopping server: Undertow - 2.3.18.Final
2025-09-10 17:07:22 WARN  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-09-10 17:07:22 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-09-10 17:07:22 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-10 17:07:22 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-10 17:07:22 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-10 17:07:22 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-10 17:07:22 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-10 17:07:22 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-10 17:07:22 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-10 17:07:22 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-10 17:07:22 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-10 17:07:22 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-10 17:07:22 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-10 17:07:22 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-10 17:07:22 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-09-10 17:07:22 ERROR [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.b.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8080 was already in use.

Action:

Identify and stop the process that's listening on port 8080 or configure this application to listen on another port.

2025-09-10 17:07:22 INFO  [Thread-3] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Close gracefully!
2025-09-10 17:07:22 INFO  [Thread-3] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-09-10 17:07:22 INFO  [Thread-2] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Close gracefully!
2025-09-10 17:07:22 INFO  [Thread-2] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-09-10 17:14:19 INFO  [background-preinit] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-09-10 17:14:20 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-10 17:14:20 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-10 17:14:20 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 80 ms. Found 0 Redis repository interfaces.
2025-09-10 17:14:21 WARN  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-09-10 17:14:21 WARN  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-09-10 17:14:22 WARN  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-09-10 17:14:22 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-09-10 17:14:22 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2887 ms
2025-09-10 17:14:22 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= caffeine
2025-09-10 17:14:22 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redisson
2025-09-10 17:14:22 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.redisson.Version - Redisson 3.45.1
2025-09-10 17:14:23 INFO  [redisson-netty-1-7] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.redisson.connection.ConnectionsHolder - 1 connections initialized for dbconn.sealoshzh.site/************:47683
2025-09-10 17:14:24 INFO  [redisson-netty-1-20] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.redisson.connection.ConnectionsHolder - 24 connections initialized for dbconn.sealoshzh.site/************:47683
2025-09-10 17:14:25 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-09-10 17:14:26 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] c.b.m.e.spring.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@69e49a81
2025-09-10 17:14:26 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Distribute Remote instanceId:[InstanceId{instanceId=**************:9198, stable=false}] - machineBit:[20] @ namespace:[fulfillmen-shop].
2025-09-10 17:14:26 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Distribute Remote machineState:[MachineState{machineId=17, lastTimeStamp=1757495666220}] - instanceId:[InstanceId{instanceId=**************:9198, stable=false}] - machineBit:[20] @ namespace:[fulfillmen-shop].
2025-09-10 17:14:26 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no].
2025-09-10 17:14:26 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no] is bound to thread:[DefaultPrefetchWorker-1].
2025-09-10 17:14:26 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-09-10 17:14:26 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Submit [fulfillmen-shop.order_no] jobSize:[0].
2025-09-10 17:14:26 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no].
2025-09-10 17:14:26 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no] is bound to thread:[DefaultPrefetchWorker-2].
2025-09-10 17:14:26 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no] is bound to thread:[DefaultPrefetchWorker-2] start.
2025-09-10 17:14:26 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Submit [fulfillmen-shop.user_no] jobSize:[0].
2025-09-10 17:14:27 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.undertow - starting server: Undertow - 2.3.18.Final
2025-09-10 17:14:27 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.xnio - XNIO version 3.8.16.Final
2025-09-10 17:14:27 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-09-10 17:14:27 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-09-10 17:14:27 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.undertow - stopping server: Undertow - 2.3.18.Final
2025-09-10 17:14:27 WARN  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-09-10 17:14:27 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-09-10 17:14:27 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-10 17:14:27 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-10 17:14:27 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-10 17:14:27 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-10 17:14:27 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-10 17:14:27 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-10 17:14:27 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-10 17:14:27 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-10 17:14:27 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-10 17:14:27 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-10 17:14:27 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-10 17:14:27 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-10 17:14:27 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-09-10 17:14:27 ERROR [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.b.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8080 was already in use.

Action:

Identify and stop the process that's listening on port 8080 or configure this application to listen on another port.

2025-09-10 17:14:27 INFO  [Thread-2] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Close gracefully!
2025-09-10 17:14:27 INFO  [Thread-3] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Close gracefully!
2025-09-10 17:14:27 INFO  [Thread-2] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-09-10 17:14:27 INFO  [Thread-3] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-09-10 17:14:54 INFO  [background-preinit] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-09-10 17:14:55 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-10 17:14:55 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-10 17:14:55 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 76 ms. Found 0 Redis repository interfaces.
2025-09-10 17:14:56 WARN  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-09-10 17:14:56 WARN  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-09-10 17:14:57 WARN  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-09-10 17:14:57 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-09-10 17:14:57 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2874 ms
2025-09-10 17:14:57 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= caffeine
2025-09-10 17:14:57 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redisson
2025-09-10 17:14:57 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.redisson.Version - Redisson 3.45.1
2025-09-10 17:14:58 INFO  [redisson-netty-1-7] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.redisson.connection.ConnectionsHolder - 1 connections initialized for dbconn.sealoshzh.site/************:47683
2025-09-10 17:14:59 INFO  [redisson-netty-1-20] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.redisson.connection.ConnectionsHolder - 24 connections initialized for dbconn.sealoshzh.site/************:47683
2025-09-10 17:15:00 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-09-10 17:15:01 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] c.b.m.e.spring.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@60acd609
2025-09-10 17:15:01 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Distribute Remote instanceId:[InstanceId{instanceId=**************:9935, stable=false}] - machineBit:[20] @ namespace:[fulfillmen-shop].
2025-09-10 17:15:01 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Distribute Remote machineState:[MachineState{machineId=18, lastTimeStamp=1757495701090}] - instanceId:[InstanceId{instanceId=**************:9935, stable=false}] - machineBit:[20] @ namespace:[fulfillmen-shop].
2025-09-10 17:15:01 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no].
2025-09-10 17:15:01 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no] is bound to thread:[DefaultPrefetchWorker-1].
2025-09-10 17:15:01 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-09-10 17:15:01 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Submit [fulfillmen-shop.order_no] jobSize:[0].
2025-09-10 17:15:01 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no].
2025-09-10 17:15:01 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no] is bound to thread:[DefaultPrefetchWorker-2].
2025-09-10 17:15:01 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no] is bound to thread:[DefaultPrefetchWorker-2] start.
2025-09-10 17:15:01 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Submit [fulfillmen-shop.user_no] jobSize:[0].
2025-09-10 17:15:02 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.undertow - starting server: Undertow - 2.3.18.Final
2025-09-10 17:15:02 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.xnio - XNIO version 3.8.16.Final
2025-09-10 17:15:02 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-09-10 17:15:02 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-09-10 17:15:02 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-09-10 17:15:02 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-09-10 17:15:02 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 66 ms
2025-09-10 17:15:02 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 13 endpoints beneath base path '/actuator'
2025-09-10 17:15:02 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.undertow - starting server: Undertow - 2.3.18.Final
2025-09-10 17:15:02 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 8099 (http) with context path '/'
2025-09-10 17:15:04 INFO  [scheduling-1] [TenantId:10000,LoginId:,IP:,OS:,Browser:] [TraceId:1117864662858821632,SpanId:,BizId:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-09-10 17:15:05 INFO  [scheduling-1] [TenantId:10000,LoginId:,IP:,OS:,Browser:] [TraceId:1117864662858821632,SpanId:,BizId:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@7d940dc1
2025-09-10 17:15:05 INFO  [scheduling-1] [TenantId:10000,LoginId:,IP:,OS:,Browser:] [TraceId:1117864662858821632,SpanId:,BizId:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-09-10 17:15:05 INFO  [RMI TCP Connection(4)-*************] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-10 17:15:42 WARN  [vt#13] [TenantId:10000,LoginId:,IP:127.0.0.1,OS:OSX,Browser:Chrome *********] [TraceId:,SpanId:,BizId:] io.micrometer.core.instrument.MeterRegistry - [1117864820728229888]:[0] This Gauge has been already registered (MeterId{name='alibaba.api.concurrent.requests', tags=[]}), the registration will be ignored. Note that subsequent logs will be logged at debug level.
2025-09-10 17:53:40 INFO  [Thread-3] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Close gracefully!
2025-09-10 17:53:40 INFO  [Thread-2] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Close gracefully!
2025-09-10 17:53:40 INFO  [Thread-3] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-09-10 17:53:40 INFO  [Thread-2] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-09-10 17:53:40 INFO  [Thread-3] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-10 17:53:40 INFO  [Thread-3] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-10 17:53:40 INFO  [Thread-3] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-10 17:53:40 INFO  [Thread-3] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-10 17:53:40 INFO  [Thread-3] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-10 17:53:40 INFO  [Thread-3] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-10 17:53:40 INFO  [Thread-3] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-10 17:53:40 INFO  [Thread-3] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-10 17:53:40 INFO  [Thread-3] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-10 17:53:40 INFO  [Thread-3] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-10 17:53:40 INFO  [Thread-3] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-10 17:53:40 INFO  [Thread-3] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-10 17:53:40 INFO  [SpringApplicationShutdownHook] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.boot.web.embedded.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-09-10 17:53:40 INFO  [SpringApplicationShutdownHook] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.boot.web.embedded.undertow.UndertowWebServer - Graceful shutdown complete
2025-09-10 17:53:40 INFO  [SpringApplicationShutdownHook] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.undertow - stopping server: Undertow - 2.3.18.Final
2025-09-10 17:53:40 INFO  [SpringApplicationShutdownHook] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Revert Remote [MachineState{machineId=18, lastTimeStamp=1757498020320}] instanceId:[InstanceId{instanceId=**************:9935, stable=false}] @ namespace:[fulfillmen-shop].
2025-09-10 17:53:40 INFO  [SpringApplicationShutdownHook] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-09-10 17:53:40 INFO  [SpringApplicationShutdownHook] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.boot.web.embedded.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-09-10 17:53:40 INFO  [SpringApplicationShutdownHook] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.boot.web.embedded.undertow.UndertowWebServer - Graceful shutdown complete
2025-09-10 17:53:40 INFO  [SpringApplicationShutdownHook] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.undertow - stopping server: Undertow - 2.3.18.Final
2025-09-10 17:53:40 INFO  [SpringApplicationShutdownHook] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-09-10 17:53:42 INFO  [SpringApplicationShutdownHook] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-09-10 17:53:42 INFO  [SpringApplicationShutdownHook] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-09-10 17:53:42 INFO  [SpringApplicationShutdownHook] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
