# AI 智能提示
> 💡 **重要**: 编码的时候，请优先读取当前系统日期。
> 💡 **重要**: 在协助开发时，请务必先阅读 `.cursor/rules/` 目录下的详细开发规范：
>
> - 📖 **项目架构**: `.cursor/rules/project-architecture.mdc` - 了解整体架构设计
> - 🏗️ **模块依赖**: `.cursor/rules/module-dependencies.mdc` - 掌握模块间依赖关系
> - 📋 **开发标准**: `.cursor/rules/development-standards.mdc` - 遵循编码规范
> - 🔄 **开发流程**: `.cursor/rules/development-workflow.mdc` - 理解开发工作流
> - 🏷️ **分层规范**: `.cursor/rules/common-domain-layers.mdc` - 分层架构约定
> - ⚡ **最佳实践**: `.cursor/rules/spring-boot-3-best-practices.mdc` - Spring Boot 3 实践
> - 🚀 **快速参考**: `.cursor/rules/quick-reference.mdc` - 常用参考手册
> - 🔄 **异常处理**: `.cursor/rules/exception-handling.mdc` - 异常处理规范
> - 🌐 **国际化规范**: `.cursor/rules/i18n-internationalization.mdc` - 多语言国际化开发指南
> **使用方法**: 根据具体开发任务，优先参考相应的详细规范文档，然后结合本文档的核心规则进行开发。

---

> 💡 **开发提示**:
> 1. 严格按照分层架构开发，遵循依赖规则
> 2. 使用现代Java特性提高代码质量
> 3. 合理使用缓存和事务管理
> 4. 遵循命名规范和代码风格
> 5. 参考 `.cursor/rules/` 获取详细指导
> 6. 启动测试用例需要 mvnd test -Ptest 执行 test profile

> **使用方法**: 根据具体开发任务，优先参考相应的详细规范文档，然后结合本文档的核心规则进行开发。

# Fulfillmen Shop 项目开发规范

## 📚 快速查看指南
- 📖 **详细开发指南**: 查看 `.cursor/rules/` 文件夹获取完整开发规范
- ⚡ **核心规范**: 本文档提供日常开发的核心提示词规则
- 💡 **使用建议**: 开发时参考本规范，深入学习可查看详细指南
- 🌐 **国际化开发**: 参考 `.cursor/rules/i18n-internationalization.mdc` 了解多语言支持

## 🚀 项目概述
- **项目名称**: Fulfillmen Shop - 采购管理系统
- **技术栈**: Spring Boot 3.3.7 + Java 17+ + MyBatis Plus + JetCache + Sa-Token + MySQL 8.0 + Redis
- **架构模式**: DDD分层架构 + Maven多模块
- **开发环境**: IntelliJ IDEA + Maven + Docker

## 📁 模块架构与职责

### 核心依赖链
```
bootstrap → [system, openapi] → manager → dao → domain → common
```

### 模块职责表
| 模块 | 主要职责 | 关键组件 |
|------|----------|----------|
| **bootstrap** | 应用启动、配置汇总 | BootstrapApplication.java |
| **system/frontend-web** | REST API、请求处理 | Controller、参数校验 |
| **system/frontend-service** | 业务逻辑、事务管理 | Service、缓存策略 |
| **manager** | 业务管理、第三方集成 | Manager、外部API |
| **dao** | 数据访问、SQL映射 | Mapper、MyBatis Plus |
| **domain** | 领域模型、数据传输 | Entity、DTO、Req/Res |
| **common** | 公共组件、工具类 | Utils、Constants、Config |

### 严格遵循的依赖规则
- ✅ 上层可依赖下层，下层不依赖上层
- ❌ 禁止循环依赖
- ❌ domain/common 不依赖任何项目内模块
- ❌ dao 不依赖 manager/service/web

## 🎯 开发规范与最佳实践

### 1. Controller层规范
```java
@Tag(name = "模块名称", description = "模块描述")
@RestController
@RequestMapping("/api/模块")
@Slf4j
public class XxxController {

private final IXxxService xxxService ;

// 构造器注入，推荐方式
public XxxController(IXxxService xxxService) {
this.xxxService = xxxService                   ;
}

@Operation(summary = "接口说明", description = "详细描述")
@GetMapping("/list")
public List<XxxRes> getList(@Valid XxxReq request) {
return xxxService.getList(request)                                 ;
}

@SaCheckLogin // 需要登录时添加
@PostMapping
public ResponseEntity<Void> create(@Valid @RequestBody CreateXxxReq request) {
xxxService.create(request)                                                     ;
return ResponseEntity.ok().build()                                             ;
}
}
```

**Controller规范要点**:
- 使用 `@Tag` 和 `@Operation` 提供API文档
- 构造器注入依赖，不使用 `@Autowired`
- 参数校验使用 `@Valid`、`@NotNull`、`@NotBlank`
- 根据需要添加 `@SaCheckLogin` 或 `@SaIgnore`
- 返回类型直接返回业务对象

### 2. Service层规范
```java
@Slf4j
@Service
public class XxxServiceImpl implements IXxxService {

private final XxxMapper xxxMapper    ;
private final IXxxManager xxxManager ;

private static final String CACHE_KEY_PREFIX = "xxx:";

public XxxServiceImpl(XxxMapper xxxMapper, IXxxManager xxxManager) {
this.xxxMapper = xxxMapper                                           ;
this.xxxManager = xxxManager                                         ;
}

@Override
@Cached(name = CACHE_KEY_PREFIX, key = "#id", expire = 3600)
@CacheRefresh(refresh = 3000, refreshLockTimeout = 20)
public XxxRes getById(Long id) {
log.info("查询xxx详情, id: {}", id)                      ;

// 1. 参数校验
if (id == null || id <= 0) {
throw new IllegalArgumentException("ID不能为空");
}

// 2. 查询数据
Xxx entity = xxxMapper.selectById(id)           ;
if (entity == null) {
throw new BusinessException("数据不存在");
}

// 3. 数据转换
return XxxMapping.INSTANCE.toRes(entity) ;
}

@Override
@Transactional(rollbackFor = Exception.class)
public void create(CreateXxxReq request) {
// 1. 数据转换
Xxx entity = XxxMapping.INSTANCE.toEntity(request) ;
entity.setUserId(UserContextHolder.getUserId())    ;

// 2. 业务处理
xxxManager.processBusinessLogic(entity) ;

// 3. 保存数据
xxxMapper.insert(entity) ;

log.info("创建xxx成功, id: {}", entity.getId()) ;
}
}
```

**Service规范要点**:
- 使用 `@Slf4j` 记录日志
- 事务方法添加 `@Transactional(rollbackFor = Exception.class)`
- 合理使用缓存注解 `@Cached`、`@CacheUpdate`、`@CacheInvalidate`
- 使用 MapStruct 进行对象转换
- 获取当前用户: `UserContextHolder.getUserId()`

### 3. Manager层规范
```java
@Component
@Slf4j
public class XxxManagerImpl implements IXxxManager {

private final IExternalService externalService ;
private final XxxMapper xxxMapper              ;

public XxxManagerImpl(IExternalService externalService, XxxMapper xxxMapper) {
this.externalService = externalService                                         ;
this.xxxMapper = xxxMapper                                                     ;
}

@Override
public ProcessResult processBusinessLogic(BusinessParam param) {
// 1. 调用第三方服务
ExternalData data = externalService.fetchData(param.getExternalId()) ;

// 2. 业务规则处理
if (!validateBusinessRule(data)) {
throw new BusinessException("业务规则校验失败");
}

// 3. 数据处理
return transformData(data) ;
}
}
```

### 4. Mapper层规范
```java
@Mapper
public interface XxxMapper extends BaseMapper<Xxx> {

// 使用 LambdaQueryWrapper 进行条件查询
default List<Xxx> selectByCondition(QueryCondition condition) {
LambdaQueryWrapper<Xxx> queryWrapper = new LambdaQueryWrapper<>()                            ;
queryWrapper.eq(StringUtils.hasText(condition.getName()), Xxx::getName, condition.getName())
.between(condition.getStartTime() != null && condition.getEndTime() != null,
Xxx::getCreateTime, condition.getStartTime(), condition.getEndTime())
.orderByDesc(Xxx::getCreateTime)                                                             ;
return selectList(queryWrapper)                                                              ;
}

// 复杂查询使用 @Select 注解或 XML
@Select("SELECT * FROM xxx WHERE status = #{status} AND user_id = #{userId}")
List<Xxx> selectByStatusAndUserId(@Param("status") Integer status, @Param("userId") Long userId) ;
}
```

## 🏷️ 核心注解规范

### Spring Boot 3 核心注解
```java
// 应用启动
@SpringBootApplication
@EnableScheduling
@EnableAsync

// 配置类
@Configuration
@EnableConfigurationProperties({XxxProperties.class})

// 服务层
@Service
@Transactional(rollbackFor = Exception.class)

// 控制器
@RestController
@RequestMapping("/api/v1")
@Validated

// 数据访问
@Mapper
@Repository
```

### 权限控制注解
```java
@SaIgnore // 公开接口，无需登录
@SaCheckLogin // 需要登录
@SaCheckRole("admin") // 需要管理员角色
@SaCheckPermission("user:delete") // 需要特定权限
```

### 缓存注解
```java
@Cached(name = "user:", key = "#id", expire = 3600) // 查询缓存
@CacheUpdate(name = "user:", key = "#result.id", value = "#result") // 更新缓存
@CacheInvalidate(name = "user:", key = "#id") // 删除缓存
@CacheRefresh(refresh = 3000, refreshLockTimeout = 20) // 自动刷新
```

### 文档注解
```java
@Tag(name = "用户管理", description = "用户相关接口")
@Operation(summary = "创建用户", description = "创建新用户")
@Schema(description = "用户信息", example = "张三")
@ApiResponse(responseCode = "200", description = "操作成功")
```

### 校验注解
```java
@Valid // 对象校验
@NotNull(message = "不能为空") // 非空校验
@NotBlank(message = "不能为空字符串") // 非空字符串
@Size(min = 1, max = 100, message = "长度限制") // 长度校验
@Min(value = 1, message = "最小值限制") // 最小值
@Pattern(regexp = "^\\d+$", message = "格式错误") // 正则校验
```

### 国际化注解
```java
// 全局国际化异常
throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.PARAM_ERROR, "username");

// OpenAPI专用国际化异常
throw OpenapiExceptionI18n.of(OpenapiErrorCodeEnum.OPENAPI_ACCOUNT_NOT_FOUND);

// 手动设置语言环境
I18nUtils.setCurrentLocale(Locale.SIMPLIFIED_CHINESE);

// Filter自动语言检测 - 无需手动配置
@WebFilter(urlPatterns = "/*") // 由I18nGlobalFilter处理
```

## 🎨 现代Java特性使用

### Java 17+ 新特性
```java
// Records - 简化数据类
public record UserInfo(Long id, String name, String email) {}

// Pattern Matching - 类型判断
if (obj instanceof String str && str.length() > 5) {
return str.toUpperCase()                             ;
}

// Switch 表达式
String result = switch (status) {
case PENDING -> "处理中";
case COMPLETED -> "已完成";
case FAILED -> "失败";
default -> "未知";
}                                 ;

// Text Blocks - 多行字符串
String sql = """
SELECT u.id, u.name, u.email
FROM users u
WHERE u.status = ?
ORDER BY u.create_time DESC
""";
```

## 📋 命名规范

### 类命名规范
| 类型 | 规范 | 示例 |
|------|------|------|
| 启动类 | XxxApplication | BootstrapApplication |
| 控制器 | XxxController | UserController |
| 服务接口 | IXxxService | IUserService |
| 服务实现 | XxxServiceImpl | UserServiceImpl |
| 管理器 | IXxxManager | IUserManager |
| 数据访问 | XxxMapper | UserMapper |
| 实体类 | 表名驼峰 | TzUser |
| DTO对象 | XxxDTO | UserCreateDTO |
| 请求对象 | XxxReq | CreateUserReq |
| 响应对象 | XxxRes | UserListRes |

### 方法命名规范
```java
// Controller层 - 使用动词，体现HTTP方法
@GetMapping("/users")
public List<UserRes> getUsers() {}

@PostMapping("/users")
public void createUser() {}

@PutMapping("/users/{id}")
public void updateUser() {}

@DeleteMapping("/users/{id}")
public void deleteUser() {}

// Service层 - 使用动词，体现业务操作
public UserRes findById(Long id) {}
public void saveUser(CreateUserReq req) {}
public void updateUser(UpdateUserReq req) {}
public void removeUser(Long id) {}

// Manager层 - 使用动词，体现业务处理
public ProcessResult processUserData(UserData data) {}
public ValidationResult validateUserInfo(UserInfo info) {}
```

## 🛠️ 开发工具配置

### Maven命令
```bash
# 编译检查
mvn clean compile

# 代码格式化
mvn spotless:apply

# 快速打包
mvn clean package -DskipTests

# 启动应用
mvn spring-boot:run -pl fulfillmen-shop-bootstrap

# 生成MapStruct实现类
mvn clean compile -pl fulfillmen-shop-domain
```

### IDEA配置建议
- 启用 Java 17+ 语言级别
- 安装 Lombok 插件
- 安装 MapStruct 插件
- 配置 Spotless 代码格式化
- 启用自动导入优化

## 🔗 常用链接
- 🌐 API文档: http://localhost:8080/swagger-ui.html
- 💚 健康检查: http://localhost:8080/actuator/health
- 📊 应用信息: http://localhost:8080/actuator/info
- 📈 性能监控: http://localhost:8080/actuator/metrics

## 📝 Git提交规范
```bash
# 提交格式
<type>(<scope>): <subject>

# 类型说明
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 重构
test: 测试相关
chore: 构建、工具等

# 示例
feat(user): 添加用户登录功能
fix(cart): 修复购物车数量计算错误
docs(readme): 更新部署文档
```

## 🚨 常见问题解决

### 1. 循环依赖问题
- 检查模块依赖关系是否符合规范
- 使用事件发布/订阅解耦
- 重新设计模块边界

### 2. 缓存使用问题
- 合理设置缓存过期时间
- 注意缓存一致性
- 使用缓存预热避免冷启动

### 3. 事务管理问题
- 确保添加 `@Transactional` 注解
- 指定 `rollbackFor = Exception.class`
- 避免跨多个数据源的事务

### 4. 国际化问题
- 使用国际化异常类而非硬编码消息
- 确保资源文件路径和命名正确
- 验证语言检测优先级设置
- 检查ThreadLocal是否正确清理

### 请严格遵循以下架构规范：
- 保持代码分层清晰(Controller -> Service -> Manager || Repository)
- 遵循 DDD (Domain-Driven Design) 设计理念
- 使用 interface 定义服务契约
- 遵循依赖倒置原则，面向接口编程
- 使用 Spring WebFlux 时保持响应式编程风格的一致性
- 结构化Spring Boot应用:控制器(controllers)、服务(services)、仓库(repositories)、模型(models)、配置(configurations)。
- 编写干净、高效且文档齐全的Java代码，并附上准确的Spring Boot示例。
- 在代码中使用Spring Boot的最佳实践和约定。
- 创建Web服务时，遵循RESTful API设计模式。
- 使用描述性的变量和方法名，遵循驼峰命名法

### Spring Boot 特定要求

- 使用Spring Boot Starters进行快速项目设置和依赖管理。
- 正确使用注解(例如:@SpringBootApplication, @RestController, @Service)。
- 有效利用Spring Boot的自动配置功能。
- 使用@ControllerAdvice和@ExceptionHandler实现正确的异常处理。

### 命名约定

- 类名使用帕斯卡命名法(例如:UserController, OrderService)。
- 方法和变量名使用驼峰命名法(例如:findUserById, isOrderValid)。
- 常量使用全大写命名(例如:MAX_RETRY_ATTEMPTS, DEFAULT_PAGE_SIZE)。

### 代码风格要求

- 在适用时使用Java 17或更高版本的特性(例如:records, sealed classes, pattern matching)。
- 优先使用不可变对象设计
- 方法参数使用 @NonNull 等注解标注
- 使用 Lombok 精简代码但不滥用
- 保持方法的单一职责
- 优先使用构造器注入
- 利用Spring Boot 3.x的特性和最佳实践。
- 对数据库操作使用MyBatisPlus + 注解方式实现。
- 使用Bean Validation(例如:@Valid, 自定义验证器)实现正确的验证。

### 配置和属性

- 使用application.properties或application.yml进行配置。
- 使用Spring Profiles实现环境特定的配置。
- 使用@ConfigurationProperties实现类型安全的配置属性。

### 依赖注入和IoC

- 优先使用构造器注入而非字段注入以提高可测试性。
- 利用Spring的IoC容器管理bean生命周期。

### Vue3 + TypeScript 开发规范

- 使用 Composition API + <script setup>
- 严格的类型定义，避免 any
- 使用 Vue Router 的类型安全导航
- Pinia 状态管理遵循模块化
- 组件设计遵循原子设计理念

### 异常处理规范

- 使用自定义业务异常
- 全局统一异常处理
- 异常信息国际化
- 详细的错误日志

### 国际化(I18n)规范

- 使用全局Filter进行语言检测
- 分层错误码体系（全局1000-1399，OpenAPI 10000-10399）
- 资源文件分离（全局message.properties + 模块专用）
- 国际化异常基类BusinessExceptionI18n
- 支持中英文自动切换，默认英文

### 测试

- 使用JUnit 5和Spring Boot Test编写单元测试。
- 使用MockMvc测试Web层。
- 使用@SpringBootTest实现集成测试。
- 对仓库层测试使用@DataJpaTest。
- 单元测试覆盖率 > 80%
- 集成测试场景完整
- 使用 TestContainers 进行集成测试
- 模拟测试数据使用 Test Fixtures

### 缓存使用规范

- JetCache 注解使用规范
- 多级缓存策略（本地缓存 + 分布式缓存）
- 缓存更新策略
- 防止缓存穿透、击穿、雪崩

### 性能和可扩展性

- 使用Spring Cache抽象实现缓存策略。
- 使用@Async进行异步处理，实现非阻塞操作。
- 实现正确的数据库索引和查询优化。

#### 性能优化准则

- 合理使用异步编程
- 数据库索引优化
- N+1 查询问题避免
- 大数据量分页处理

### 安全

- 使用Spring Security进行认证和授权。
- 使用正确的密码编码(例如:BCrypt)。
- 在需要时实现CORS配置。
- 输入验证和转义
- SQL 注入防护
- XSS 防护
- CSRF 防护
- 敏感数据加密

### 日志和监控

- 使用SLF4J和Logback进行日志记录。
- 实现正确的日志级别(ERROR, WARN, INFO, DEBUG)。
- 使用Spring Boot Actuator进行应用监控和指标收集。

### API 设计准则

- RESTful API 设计规范
- 使用 DTO 对象进行数据传输
- 统一的响应格式
- 请求参数校验
- API 版本控制
- 完整的 Swagger/OpenAPI 文档

### 数据访问和ORM

- 对数据库操作使用MyBatisPlus + 注解方式实现。
- 实现正确的实体关系和级联操作。
- 使用Flyway或Liquibase进行数据库迁移。

### 构建和部署

- 使用Maven进行依赖管理和构建过程。
- 为不同环境(开发、测试、生产)实现正确的配置。
- 在适用时使用Docker进行容器化。

### 工程管理规范
- Maven 模块划分清晰
- 依赖版本统一管理
- 资源文件规范放置
- 配置文件分环境

### 文档要求

- 类和方法必须有完整的 Javadoc
- 复杂业务逻辑需要添加详细注释
- README 文档包含必要的项目信息
- 关键业务流程需要有流程图说明

### 遵循最佳实践

- 遵循RESTful API设计(正确使用HTTP方法、状态码等)。
- 使用Spring的@Async或Spring WebFlux进行异步处理。

### 代码审查要点

- 命名规范性
- 代码复杂度
- 重复代码检查
- 潜在的性能问题
- 安全漏洞检查

遵循SOLID原则, 保持Spring Boot应用设计中的高内聚和低耦合。

编写文档的存放，统一在 docs/.cursor-chat/{当前系统日期 YYYY-MM-DD} 目录下，每个任务一个文件，文件名称为 任务名称.md ，文件内容为任务描述和任务步骤。


---
