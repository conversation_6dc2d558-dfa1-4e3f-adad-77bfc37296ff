# 构建阶段
FROM crpi-irb3zn66qvxjqsx0.cn-shenzhen.personal.cr.aliyuncs.com/fulfillmen/maven:3.9-eclipse-temurin-17 AS builder
WORKDIR /workspace

# Maven 设置文件
# 创建Maven配置目录
RUN mkdir -p /root/.m2/

# 配置Maven设置
# 1. 从构建参数获取settings.xml内容（优先）
ARG MAVEN_SETTINGS
RUN if [ ! -z "$MAVEN_SETTINGS" ]; then \
      echo "$MAVEN_SETTINGS" > /root/.m2/settings.xml && \
      echo "Using provided Maven settings from build arg"; \
    fi

# 添加标记文件保证后续步骤正常运行
RUN touch /root/.m2/.settings_configured || true

# 复制整个项目目录（除了.dockerignore中排除的文件）
COPY . .

# 尝试使用本地settings.xml（如果存在且没有通过构建参数提供）
RUN if [ -f settings.xml ] && [ ! -s /root/.m2/settings.xml ]; then \
      cp settings.xml /root/.m2/settings.xml && \
      echo "Using settings.xml from project root"; \
    elif [ ! -s /root/.m2/settings.xml ]; then \
      echo "No custom settings.xml found, using default Maven settings"; \
    fi

# 构建项目
RUN mvn clean package -DskipTests -U

# 提取分层
ARG JAR_FILE=fulfillmen-shop-bootstrap/target/fulfillmen-shop.jar
RUN if [ -f "${JAR_FILE}" ]; then \
        mkdir -p target && java -Djarmode=layertools -jar ${JAR_FILE} extract; \
    else \
        echo "错误: 无法找到JAR文件: ${JAR_FILE}" && \
        echo "目录内容:" && \
        find fulfillmen-shop-bootstrap -type f -name "*.jar" || echo "未找到JAR文件" && \
        exit 1; \
    fi

# 运行阶段
FROM crpi-irb3zn66qvxjqsx0.cn-shenzhen.personal.cr.aliyuncs.com/fulfillmen/java:eclipse-temurin-17.0.10_7
WORKDIR /app

# 创建非root用户
RUN addgroup --system --gid 1000 appuser && \
    adduser --system --uid 1000 --ingroup appuser appuser && \
    mkdir -p /app/logs /app/heapdump /app/config && \
    chown -R appuser:appuser /app

# 复制分层的应用到/app目录
COPY --from=builder /workspace/dependencies/ ./
COPY --from=builder /workspace/spring-boot-loader/ ./
COPY --from=builder /workspace/snapshot-dependencies/ ./
COPY --from=builder /workspace/application/ ./

# 复制配置文件
COPY --from=builder /workspace/fulfillmen-shop-bootstrap/src/main/resources/config/ /app/config/

# 创建并复制启动脚本
COPY --from=builder /workspace/fulfillmen-shop-bootstrap/src/main/docker/entrypoint.sh /app/entrypoint.sh
RUN chmod +x /app/entrypoint.sh && \
    chown appuser:appuser /app/entrypoint.sh

# 设置环境变量
ENV TZ=Asia/Shanghai \
    JAVA_OPTS="-XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/app/heapdump" \
    ACTIVE=dev \
    SERVER_PORT=8080

# 切换到非root用户
USER appuser

# 健康检查
HEALTHCHECK --interval=30s --timeout=5s --start-period=40s --retries=3 \
  CMD curl -f http://localhost:${SERVER_PORT}/actuator/health || exit 1

# 暴露端口
EXPOSE 8080

# 使用tini作为入口点，通过entrypoint.sh启动应用
ENTRYPOINT ["/usr/bin/tini", "--", "/app/entrypoint.sh"]