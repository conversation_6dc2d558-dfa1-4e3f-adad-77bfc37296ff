/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.frontend.service.impl;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheManager;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.template.QuickConfig;
import com.fulfillmen.shop.common.enums.FulfillmenErrorCodeEnum;
import com.fulfillmen.shop.common.enums.UserBehaviorEnums;
import com.fulfillmen.shop.common.exception.BusinessExceptionI18n;
import com.fulfillmen.shop.dao.mapper.TzProductSpuMapper;
import com.fulfillmen.shop.domain.dto.PageDTO;
import com.fulfillmen.shop.domain.dto.ProductInfoDTO;
import com.fulfillmen.shop.domain.dto.TzProductDTO;
import com.fulfillmen.shop.domain.req.AggregateSearchReq;
import com.fulfillmen.shop.domain.req.FreightEstimateReq;
import com.fulfillmen.shop.domain.util.CurrencyConversionUtils;
import com.fulfillmen.shop.domain.vo.FreightEstimateResultVO;
import com.fulfillmen.shop.domain.vo.ProductDetailVO;
import com.fulfillmen.shop.domain.vo.ProductInfoVO;
import com.fulfillmen.shop.frontend.annotation.UserBehavior;
import com.fulfillmen.shop.frontend.convert.FrontendProductConvert;
import com.fulfillmen.shop.frontend.service.IProductService;
import com.fulfillmen.shop.frontend.service.user.IUserHistoryService;
import com.fulfillmen.shop.manager.core.repository.PdcProductMappingRepository;
import com.fulfillmen.shop.manager.product.service.IProductFacadeService;
import com.fulfillmen.shop.manager.support.alibaba.IProductManager;
import com.fulfillmen.support.alibaba.api.response.logistics.ProductFreightEstimateResponse;
import java.math.BigDecimal;
import java.time.Duration;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.Executor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

/**
 * 商品搜索服务实现
 *
 * <AUTHOR>
 * @date 2025/2/25 19:15
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProductServiceImpl implements IProductService, InitializingBean {

    private final IProductManager productManager;
    private final IProductFacadeService productFacadeService;
    private final CacheManager cacheManager;
    private final PdcProductMappingRepository pdcProductMappingRepository;
    private final TzProductSpuMapper tzProductSpuMapper;
    private final IUserHistoryService historyService;
    private final Executor virtualThreadExecutor;

    /**
     * Redis缓存 - ProductDetailVO (15分钟)
     */
    private Cache<String, ProductDetailVO> productDetailVOCache;

    /**
     * 上传图片获取图片ID
     *
     * <pre>
     * 1. 文件类型校验 (仅允许图片格式)
     * 2. 文件大小校验 (限制2MB)
     * 3. 图片转Base64编码
     * 4. 调用底层服务上传图片
     * 5. 异常处理逻辑
     * </pre>
     *
     * @param file 图片文件
     * @return 图片ID，用于后续图片搜索
     */
    @Override
    public String uploadImage(MultipartFile file) {
        log.debug("uploadImage request: filename={}, size={}, contentType={}", file.getOriginalFilename(), file
            .getSize(), file.getContentType());
        return productManager.uploadImage(file);
    }

    @Override
    public PageDTO<ProductInfoDTO> searchSimilarProductsSyncDb(Long offerId, Integer pageIndex, Integer pageSize) {
        // 注意：这里保留直接调用Repository，因为相似商品搜索是批量操作，
        // 不适合通过ProductSyncService的单个产品同步逻辑处理
        return productFacadeService.searchSimilarProducts(offerId, pageIndex, pageSize);
    }

    @Override
    public PageDTO<ProductInfoVO> unifiedAggregateSearch(AggregateSearchReq request, Boolean useCache) {
        log.info("聚合搜索VO: searchType={}, keyword={}", request.getSearchType(), request.getKeyword());

        try {
            // 1. 使用 PdcProductMappingRepository 获取聚合搜索结果
            // 注意：聚合搜索是批量操作，保留直接调用Repository
            PageDTO<ProductInfoDTO> searchResult = productFacadeService.unifiedAggregateSearch(request, !useCache);

            if (searchResult == null || CollectionUtils.isEmpty(searchResult.getRecords())) {
                log.debug("聚合搜索结果为空: searchType={}, keyword={}", request.getSearchType(), request.getKeyword());
                return PageDTO.<ProductInfoVO>builder()
                    .records(List.of())
                    .pageIndex(request.getPage() != null ? request.getPage() : 1)
                    .pageSize(request.getPageSize() != null ? request.getPageSize() : 10)
                    .total(0L)
                    .build();
            }

            // 3. 转换为前端VO
            PageDTO<ProductInfoVO> voPage = FrontendProductConvert.INSTANCE.toProductInfoVOPage(searchResult);

            log.debug("聚合搜索VO完成，结果数量: {}", voPage.getRecords().size());
            return voPage;

        } catch (Exception e) {
            log.error("聚合搜索VO异常: searchType={}, keyword={}", request.getSearchType(), request.getKeyword(), e);
            return PageDTO.<ProductInfoVO>builder()
                .records(List.of())
                .pageIndex(request.getPage() != null ? request.getPage() : 1)
                .pageSize(request.getPageSize() != null ? request.getPageSize() : 10)
                .total(0L)
                .build();
        }
    }

    /**
     * 🎯 Task 1.4: 清理产品详情缓存
     *
     * @param platformProductId 平台产品ID，null表示清理所有
     * @return 清理结果描述
     */
    @Override
    public String clearProductDetailCache(String platformProductId) {
        try {
            if (platformProductId != null) {
                // 清理指定产品的缓存
                String cacheKey = "productDetail:" + platformProductId;

                productDetailVOCache.remove(cacheKey);

                log.info("已清理产品详情缓存: platformProductId={}", platformProductId);
                return "已清理产品 " + platformProductId + " 的缓存";
            } else {
                // 清理所有缓存 - 注意：这个操作比较重，建议谨慎使用
                log.warn("请求清理所有产品详情缓存");
                return "清理所有缓存功能暂未实现，请指定具体的产品ID";
            }
        } catch (Exception e) {
            log.error("清理产品详情缓存失败: platformProductId={}", platformProductId, e);
            return "清理缓存失败: " + e.getMessage();
        }
    }

    // ==================== 前端VO方法实现 ====================

    @Override
    @UserBehavior(userBehavior = UserBehaviorEnums.BROWSER_PRODUCT, description = "浏览商品")
    public ProductDetailVO getProductDetailVO(Long id) {
        return getProductDetailVO(id, true);
    }

    @Override
    public ProductDetailVO getProductDetailVO(Long id, boolean applyPricing) {
        log.debug("获取商品详情VO: 传入ID={}, 应用价格策略: {}", id, applyPricing);
        TzProductDTO productDTO = productFacadeService.getOrSyncProduct(String.valueOf(id));
        return FrontendProductConvert.INSTANCE.toProductDetailVOFromTzProductDTO(productDTO);
    }

    @Override
    public ProductDetailVO reSyncProductDetailVO(Long id) {
        TzProductDTO productDTO = this.productFacadeService.getOrSyncProduct(String.valueOf(id), true);
        return FrontendProductConvert.INSTANCE.toProductDetailVOFromTzProductDTO(productDTO);
    }

    /**
     * 估算商品运费
     *
     * @param req 运费估算请求参数
     * @return 运费估算结果
     */
    @Override
    public FreightEstimateResultVO estimateFreight(FreightEstimateReq req) {
        log.info("开始估算商品运费, 请求参数: {}", req);
        try {
            // 1. 判断传入的ID类型并返回实际的平台产品ID
            String platformProductId = determineActualPlatformProductId(req.getOfferId());

            FreightEstimateReq request = FreightEstimateReq.builder()
                .offerId(Long.valueOf(platformProductId))
                .toProvinceCode(req.getToProvinceCode())
                .toCityCode(req.getToCityCode())
                .toCountryCode(req.getToCountryCode())
                .totalNum(req.getTotalNum())
                .logisticsSkuNumModels(req.getLogisticsSkuNumModels())
                .build();

            // 2. 调用Manager层获取1688原始数据
            ProductFreightEstimateResponse.ProductFreightModel freightModel = productManager.estimateFreight(request);

            // 3. 转换为前端VO
            FreightEstimateResultVO vo = convertToFreightEstimateVO(freightModel);
            log.info("商品运费估算成功, offerId: {}, 运费: {}", request.getOfferId(), vo.getFreight());
            return vo;
        } catch (BusinessExceptionI18n e) {
            // 如果是已知的业务异常，直接抛出
            log.warn("商品运费估算业务异常, offerId: {}, 错误: {}", req.getOfferId(), e.getMessage());
            throw e;
        } catch (Exception e) {
            // 对于未知异常，进行包装和记录
            log.error("商品运费估算系统异常, offerId: {}", req.getOfferId(), e);
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.FREIGHT_ESTIMATE_FAILED);
        }
    }

    /**
     * 转换1688运费估算结果为前端VO
     *
     * @param freightModel 1688运费模型结果
     * @return 前端运费估算VO
     */
    private FreightEstimateResultVO convertToFreightEstimateVO(ProductFreightEstimateResponse.ProductFreightModel freightModel) {
        if (freightModel == null) {
            log.warn("运费模型为空，返回默认VO");
            return FreightEstimateResultVO.builder()
                .freight("0.00")
                .freePostage(true)
                .build();
        }

        try {
            BigDecimal exchangeRate = CurrencyConversionUtils.getExchangeRate("CNY", "USD");
            List<FreightEstimateResultVO.ProductFreightSkuInfoModel> skuInfoModels = FrontendProductConvert.INSTANCE.convertProductFreightSkuInfoModels(freightModel.getProductFreightSkuInfoModels());

            FreightEstimateResultVO vo = FrontendProductConvert.INSTANCE.toFreightEstimateResultVOWithExchangeRate(freightModel, skuInfoModels, exchangeRate);

            log.debug("运费估算VO转换完成: 运费={}, 是否包邮={}, 计费类型={}",
                vo.getFreight(), vo.getFreePostage(), vo.getChargeType());

            return vo;

        } catch (Exception e) {
            log.error("转换运费估算VO时发生异常: freightModel={}", freightModel, e);
            // 返回基础信息，确保前端能正常显示
            return FreightEstimateResultVO.builder()
                .freight(freightModel.getFreight())
                .freePostage(freightModel.getFreePostage())
                .chargeType(freightModel.getChargeType())
                .build();
        }
    }

    /**
     * 判断传入的ID类型并返回实际的平台产品ID
     *
     * @param offerId 传入的offerId（可能是pdcProductMappingId或pdcPlatformProductId）
     * @return 实际的平台产品ID（1688的offerId）
     */
    private String determineActualPlatformProductId(Long offerId) {
        if (offerId == null) {
            throw new IllegalArgumentException("offerId不能为空");
        }

        String offerIdStr = String.valueOf(offerId);

        try {
            // 1. 首先尝试作为pdcProductMappingId查询
            TzProductDTO productDTO = this.productFacadeService.getProduct(offerIdStr);
            if (Objects.nonNull(productDTO)) {
                return productDTO.getPdcPlatformProductId();
            }
            // 3. 如果都不是，记录警告并返回原始值
            log.warn("未找到对应的产品记录，offerId: {}, 将直接使用原始值", offerId);
            return offerIdStr;

        } catch (Exception e) {
            log.error("判断ID类型时发生异常, offerId: {}", offerId, e);
            // 发生异常时，返回原始值，避免影响主流程
            return offerIdStr;
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        // 🎯 Phase 1.4: 优化多级缓存配置
        initializeOptimizedCacheConfiguration();
        logCacheConfigurationSummary();
    }

    // ==================== 🎯 Phase 1.4: 优化缓存配置方法 ====================

    /**
     * 🎯 Phase 1.4: 初始化优化的缓存配置
     *
     * <pre>
     * 缓存策略优化：
     * 1. L1缓存(ProductDetailVO): 完整展示数据，中时效，减少转换开销
     * 3. 智能失效策略: 本地+远程双重保障
     * 4. 容量控制: 根据业务特点设置合理容量
     * </pre>
     */
    private void initializeOptimizedCacheConfiguration() {
        // 缓存: ProductDetailVO - 前端展示缓存
        productDetailVOCache = createProductDetailVOCache();

        log.debug("缓存配置详情: ProductDetailVO[本地:8min,远程:25min,容量:300]");
    }

    /**
     * 创建ProductDetailVO缓存配置
     *
     * <pre>
     * 优化策略：
     * - 本地缓存8分钟: 前端展示数据较少变化
     * - 远程缓存25分钟: 完整VO对象，转换成本高
     * - 容量300: 支持更多商品详情页访问
     * - 同步机制: 确保多实例数据一致性
     * </pre>
     */
    private Cache<String, ProductDetailVO> createProductDetailVOCache() {
        QuickConfig config = QuickConfig.newBuilder("frontend:product:vo:")
            // 优化: 本地缓存时间调整到8分钟，平衡用户体验和数据一致性
            .localExpire(Duration.ofMinutes(8))
            // 优化: 远程缓存时间调整到25分钟，减少重复转换
            .expire(Duration.ofMinutes(25))
            // 开启空值缓存，防止无效请求重复处理
            .cacheNullValue(true)
            // 双层缓存：本地 + 远程
            .cacheType(CacheType.BOTH)
            // 优化: 本地缓存容量增加到300，支持更多商品详情
            .localLimit(300)
            // 本地缓存同步，保证用户看到一致的商品信息
            .syncLocal(true)
            .build();
        return cacheManager.getOrCreateCache(config);
    }

    /**
     * 记录缓存配置摘要信息
     */
    private void logCacheConfigurationSummary() {
        log.debug("🎯 ProductService优化缓存配置完成:");
        log.debug("  ├── ProductDetailVO缓存: 本地8min/远程25min, 容量300, 键前缀: frontend:product:vo:");
        log.debug("  ├── 双层缓存策略: 本地内存 + Redis远程");
        log.debug("  ├── 空值缓存: 已启用，防止缓存穿透");
        log.debug("  └── 同步机制: 已启用，保证多实例一致性");
    }

}
