#!/bin/bash
# 构建镜像脚本
# 使用方法：./build-image.sh [选项]
#
# 选项:
#   -t, --tag <版本号>          指定镜像标签/版本号（默认: latest）
#   -r, --registry <地址>       指定镜像仓库地址
#   -n, --name <名称>           指定镜像名称（默认: fulfillmen-shop）
#   -p, --push                  构建后推送镜像到仓库
#   -l, --load                  构建后加载镜像到Docker
#   -c, --clean                 构建前清理Maven缓存
#   -m, --maven-settings <文件>  指定Maven settings.xml文件路径
#   -h, --help                  显示帮助信息
#   --no-cache                  不使用缓存构建镜像

set -e

# 默认值
VERSION="latest"
REGISTRY="crpi-irb3zn66qvxjqsx0.cn-shenzhen.personal.cr.aliyuncs.com/fulfillmen"
IMAGE_NAME="shop"
PUSH_IMAGE=false
LOAD_IMAGE=true
CLEAN_CACHE=false
NO_CACHE=""
MAVEN_SETTINGS_FILE=""
MAVEN_SETTINGS_CONTENT=""

# 解析命令行参数
while [[ $# -gt 0 ]]; do
  case $1 in
    -t|--tag)
      VERSION="$2"
      shift 2
      ;;
    -r|--registry)
      REGISTRY="$2"
      shift 2
      ;;
    -n|--name)
      IMAGE_NAME="$2"
      shift 2
      ;;
    -p|--push)
      PUSH_IMAGE=true
      shift
      ;;
    -l|--load)
      LOAD_IMAGE=true
      shift
      ;;
    -c|--clean)
      CLEAN_CACHE=true
      shift
      ;;
    -m|--maven-settings)
      MAVEN_SETTINGS_FILE="$2"
      shift 2
      ;;
    --no-cache)
      NO_CACHE="--no-cache"
      shift
      ;;
    -h|--help)
      echo "使用方法: $0 [选项]"
      echo ""
      echo "选项:"
      echo "  -t, --tag <版本号>          指定镜像标签/版本号（默认: latest）"
      echo "  -r, --registry <地址>       指定镜像仓库地址"
      echo "  -n, --name <名称>           指定镜像名称（默认: fulfillmen-shop）"
      echo "  -p, --push                  构建后推送镜像到仓库"
      echo "  -l, --load                  构建后加载镜像到Docker（默认启用）"
      echo "  -c, --clean                 构建前清理Maven缓存"
      echo "  -m, --maven-settings <文件> 指定Maven settings.xml文件路径"
      echo "  -h, --help                  显示帮助信息"
      echo "  --no-cache                  不使用缓存构建镜像"
      exit 0
      ;;
    *)
      echo "未知选项: $1"
      echo "使用 '$0 --help' 查看帮助"
      exit 1
      ;;
  esac
done

FULL_IMAGE_NAME="${REGISTRY}/${IMAGE_NAME}:${VERSION}"

# 输出构建信息
echo "==================================================================="
echo "开始构建 Spring Boot 应用镜像"
echo "镜像名称: ${FULL_IMAGE_NAME}"
if [ "$PUSH_IMAGE" = true ]; then
  echo "构建后推送: 是"
fi
if [ "$LOAD_IMAGE" = true ]; then
  echo "构建后加载: 是"
fi
if [ "$CLEAN_CACHE" = true ]; then
  echo "清理缓存: 是"
fi
if [ -n "$NO_CACHE" ]; then
  echo "使用缓存: 否"
fi
if [ -n "$MAVEN_SETTINGS_FILE" ]; then
  echo "Maven设置文件: ${MAVEN_SETTINGS_FILE}"
  
  # 检查Maven设置文件是否存在
  if [ ! -f "$MAVEN_SETTINGS_FILE" ]; then
    echo "错误: Maven设置文件不存在: ${MAVEN_SETTINGS_FILE}"
    exit 1
  fi
  
  # 复制settings.xml到当前目录，方便Dockerfile使用
  echo "复制Maven设置文件到构建上下文..."
  cp "$MAVEN_SETTINGS_FILE" settings.xml
fi
echo "==================================================================="

# 检查 Dockerfile 是否存在
if [ ! -f Dockerfile ]; then
  echo "错误: 当前目录下找不到 Dockerfile"
  exit 1
fi

# 清理 Maven 缓存（如果需要）
if [ "$CLEAN_CACHE" = true ]; then
  echo "清理 Maven 缓存..."
  mvn clean
fi

# 构建 Docker 镜像
BUILD_ARGS=""
if [ "$LOAD_IMAGE" = true ]; then
  BUILD_ARGS="$BUILD_ARGS --load"
fi
if [ "$PUSH_IMAGE" = true ]; then
  BUILD_ARGS="$BUILD_ARGS --push"
fi

# 如果指定了Maven设置文件，添加构建参数
if [ -n "$MAVEN_SETTINGS_FILE" ]; then
  MAVEN_SETTINGS_CONTENT=$(cat "$MAVEN_SETTINGS_FILE" | base64 -w 0)
  BUILD_ARGS="$BUILD_ARGS --build-arg MAVEN_SETTINGS='$MAVEN_SETTINGS_CONTENT'"
fi

echo "正在构建镜像 ${FULL_IMAGE_NAME}..."
echo "执行命令: docker buildx build $BUILD_ARGS $NO_CACHE -t ${FULL_IMAGE_NAME} ."

# 执行构建命令
if [ -n "$MAVEN_SETTINGS_FILE" ]; then
  # 如果有Maven设置文件，使用完整设置
  docker buildx build $BUILD_ARGS $NO_CACHE --build-arg MAVEN_SETTINGS="$(cat "$MAVEN_SETTINGS_FILE")" -t "${FULL_IMAGE_NAME}" .
else
  # 否则使用默认设置
  docker buildx build $BUILD_ARGS $NO_CACHE -t "${FULL_IMAGE_NAME}" .
fi

# 检查构建结果
if [ $? -eq 0 ]; then
  echo "==================================================================="
  echo "✅ 镜像构建成功: ${FULL_IMAGE_NAME}"
  
  # 标记为最新版本（如果版本不是latest）
  if [ "${VERSION}" != "latest" ]; then
    echo "正在标记镜像为 latest 版本..."
    docker tag "${FULL_IMAGE_NAME}" "${REGISTRY}/${IMAGE_NAME}:latest"
    echo "✅ 已标记为 latest 版本"
    
    # 如果已经指定了推送，同时也推送latest版本
    if [ "$PUSH_IMAGE" = true ]; then
      echo "推送镜像 ${REGISTRY}/${IMAGE_NAME}:latest 到仓库..."
      docker push "${REGISTRY}/${IMAGE_NAME}:latest"
    fi
  fi
  
  # 如果还没推送过镜像并且没有设置自动推送，询问是否推送
  if [ "$PUSH_IMAGE" = false ]; then
    read -p "是否要推送镜像到仓库? (y/n): " PUSH_CHOICE
    if [[ "${PUSH_CHOICE}" == "y" || "${PUSH_CHOICE}" == "Y" ]]; then
      echo "推送镜像 ${FULL_IMAGE_NAME} 到仓库..."
      docker push "${FULL_IMAGE_NAME}"
      
      if [ "${VERSION}" != "latest" ]; then
        echo "推送镜像 ${REGISTRY}/${IMAGE_NAME}:latest 到仓库..."
        docker push "${REGISTRY}/${IMAGE_NAME}:latest"
      fi
      
      echo "✅ 镜像推送完成"
    fi
  fi
  
  # 清理临时文件
  if [ -f "settings.xml" ] && [ -n "$MAVEN_SETTINGS_FILE" ]; then
    rm settings.xml
    echo "已删除临时Maven设置文件"
  fi
  
  echo "==================================================================="
  echo "构建完成时间: $(date)"
  echo "==================================================================="
else
  echo "❌ 镜像构建失败"
  
  # 清理临时文件
  if [ -f "settings.xml" ] && [ -n "$MAVEN_SETTINGS_FILE" ]; then
    rm settings.xml
    echo "已删除临时Maven设置文件"
  fi
  
  exit 1
fi