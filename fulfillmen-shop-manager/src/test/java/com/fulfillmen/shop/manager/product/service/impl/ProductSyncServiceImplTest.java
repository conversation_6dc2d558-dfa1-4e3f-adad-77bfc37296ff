/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.product.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.fulfillmen.shop.domain.dto.TzProductDTO;
import com.fulfillmen.shop.domain.dto.product.alibaba.AlibabaProductDetailDTO;
import com.fulfillmen.shop.manager.product.service.IProductSyncService;
import com.fulfillmen.shop.manager.service.dto.BatchSyncResult;
import java.math.BigDecimal;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * ProductSyncServiceImpl 测试类
 *
 * <pre>
 * 测试重点：
 * 1. 验证迁移后的核心同步功能
 * 2. 测试SKU智能更新逻辑
 * 3. 验证SKU不存在和新增的处理
 * 4. 测试错误处理和异常恢复
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/9/10
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
class ProductSyncServiceImplTest {

    @Autowired
    private IProductSyncService productSyncService;

    /**
     * 测试基本的产品同步功能
     */
    @Test
    void testSyncProduct() {
        // 使用一个测试产品ID
        String testProductId = "1234567890";

        try {
            TzProductDTO result = productSyncService.syncProduct(testProductId);

            if (result != null) {
                log.info("产品同步成功: {}", result.getTitle());
                assertNotNull(result.getId());
                assertNotNull(result.getTitle());
            } else {
                log.warn("产品同步返回null，可能是测试产品不存在");
            }
        } catch (Exception e) {
            log.error("产品同步测试失败", e);
            // 在测试环境中，这可能是正常的，因为测试产品可能不存在
        }
    }

    /**
     * 测试智能获取或同步功能
     */
    @Test
    void testGetOrSyncProduct() {
        String testProductId = "1234567890";

        try {
            TzProductDTO result = productSyncService.getOrSyncProduct(testProductId);

            if (result != null) {
                log.info("智能获取产品成功: {}", result.getTitle());
                assertNotNull(result);
            } else {
                log.warn("智能获取产品返回null");
            }
        } catch (Exception e) {
            log.error("智能获取产品测试失败", e);
        }
    }

    /**
     * 测试产品详情获取
     */
    @Test
    void testGetProductDetail() {
        String testProductId = "1234567890";

        try {
            AlibabaProductDetailDTO detail = productSyncService.getProductDetail(testProductId);

            if (detail != null) {
                log.info("获取产品详情成功: {}", detail.getTitle());
                assertNotNull(detail.getId());
            } else {
                log.warn("获取产品详情返回null");
            }
        } catch (Exception e) {
            log.error("获取产品详情测试失败", e);
        }
    }

    /**
     * 测试批量同步功能
     */
    @Test
    void testBatchSyncProducts() {
        List<String> testProductIds = List.of("1234567890", "1234567891", "1234567892");

        try {
            BatchSyncResult result = productSyncService.batchSyncProducts(testProductIds, false);

            assertNotNull(result);
            assertEquals(testProductIds.size(), result.getTotalCount());
            log.info("批量同步测试完成，总数: {}, 成功: {}, 失败: {}",
                result.getTotalCount(), result.getSuccessCount(), result.getFailureCount());
        } catch (Exception e) {
            log.error("批量同步测试失败", e);
        }
    }

    /**
     * 测试数据验证方法
     */
    @Test
    void testDataValidationMethods() {
        String testProductId = "1234567890";

        try {
            boolean isSynced = productSyncService.isProductSynced(testProductId);
            boolean needsRefresh = productSyncService.needsRefresh(testProductId);

            log.info("产品同步状态检查 - 已同步: {}, 需要刷新: {}", isSynced, needsRefresh);

            // 这些方法应该能正常执行而不抛出异常
            assertNotNull(isSynced);
            assertNotNull(needsRefresh);
        } catch (Exception e) {
            log.error("数据验证方法测试失败", e);
        }
    }

    /**
     * 测试扩展的SKU相关方法
     */
    @Test
    void testSkuRelatedMethods() {
        String testProductId = "1234567890";

        try {
            // 测试单品检查
            boolean isSingleItem = ((ProductSyncServiceImpl) productSyncService).isSingleItem(testProductId);
            log.info("单品检查结果: {}", isSingleItem);

            // 测试单品价格获取
            BigDecimal price = ((ProductSyncServiceImpl) productSyncService).getSingleItemPrice(testProductId);
            if (price != null) {
                log.info("单品价格: {}", price);
                assertTrue(price.compareTo(BigDecimal.ZERO) >= 0);
            }

        } catch (Exception e) {
            log.error("SKU相关方法测试失败", e);
        }
    }

    /**
     * 测试参数验证
     */
    @Test
    void testParameterValidation() {
        // 测试空参数
        assertThrows(IllegalArgumentException.class, () -> {
            productSyncService.syncProduct(null);
        });

        assertThrows(IllegalArgumentException.class, () -> {
            productSyncService.syncProduct("");
        });

        assertThrows(IllegalArgumentException.class, () -> {
            productSyncService.syncProduct("   ");
        });

        log.info("参数验证测试通过");
    }

    /**
     * 测试批量同步的边界情况
     */
    @Test
    void testBatchSyncEdgeCases() {
        // 测试空列表
        BatchSyncResult emptyResult = productSyncService.batchSyncProducts(null, false);
        assertNotNull(emptyResult);
        assertEquals(0, emptyResult.getTotalCount());

        // 测试空列表
        BatchSyncResult emptyListResult = productSyncService.batchSyncProducts(List.of(), false);
        assertNotNull(emptyListResult);
        assertEquals(0, emptyListResult.getTotalCount());

        log.info("批量同步边界情况测试通过");
    }
}
