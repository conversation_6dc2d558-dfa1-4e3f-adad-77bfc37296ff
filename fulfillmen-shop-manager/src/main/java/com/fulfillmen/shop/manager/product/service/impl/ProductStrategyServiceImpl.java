/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.product.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.fulfillmen.shop.common.context.UserContextHolder;
import com.fulfillmen.shop.common.tenant.EnhancedTenantContextHolder;
import com.fulfillmen.shop.domain.dto.ProductInfoDTO;
import com.fulfillmen.shop.domain.dto.TzProductDTO;
import com.fulfillmen.shop.manager.convert.product.ProductBaseConvertMapping;
import com.fulfillmen.shop.manager.product.chain.ProductProcessor;
import com.fulfillmen.shop.manager.product.chain.ProductProcessorChain;
import com.fulfillmen.shop.manager.product.context.ProductProcessingContext;
import com.fulfillmen.shop.manager.product.context.TenantContext;
import com.fulfillmen.shop.manager.product.context.UserPricingContext;
import com.fulfillmen.shop.manager.product.service.IProductStrategyService;
import com.fulfillmen.shop.manager.product.strategy.ProductStrategy;
import com.fulfillmen.shop.manager.product.strategy.ProductStrategyFactory;
import com.fulfillmen.shop.manager.product.strategy.pricing.PricingStrategy;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 产品策略服务实现
 *
 * <pre>
 * 核心功能：
 * 1. 整合责任链处理器和策略工厂
 * 2. 提供产品策略处理的统一入口
 * 3. 管理处理上下文的构建和生命周期
 * 4. 提供系统监控和统计功能
 *
 * 处理流程：
 * 1. 构建处理上下文（用户、租户、产品信息）
 * 2. 执行责任链处理（定价、库存、图片等）
 * 3. 应用策略结果并验证数据完整性
 * 4. 记录处理统计和性能指标
 *
 * 设计特点：
 * - 模块化设计：策略和处理器解耦
 * - 可扩展性：易于添加新的策略和处理器
 * - 高性能：支持缓存和并行处理
 * - 可监控：提供详细的执行统计
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/08/22
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProductStrategyServiceImpl implements IProductStrategyService {

    private final ProductProcessorChain processorChain;
    private final ProductStrategyFactory strategyFactory;

    // 执行统计
    private final AtomicInteger totalProcessCount = new AtomicInteger(0);
    private final AtomicInteger successProcessCount = new AtomicInteger(0);
    private final AtomicInteger failureProcessCount = new AtomicInteger(0);

    @Override
    public TzProductDTO processProduct(TzProductDTO product, ProductProcessingContext context) {
        if (product == null) {
            log.warn("产品策略服务: 产品数据为空");
            return null;
        }

        if (context == null) {
            log.warn("产品策略服务: 处理上下文为空");
            return product;
        }

        try {
            // 记录处理开始
            totalProcessCount.incrementAndGet();
            long startTime = System.currentTimeMillis();

            log.debug("开始处理产品策略: 产品ID={}, 用户ID={}, 租户ID={}",
                context.getProductId(), context.getUserId(), context.getTenantId());

            // 将产品数据放入上下文
            context.setAttribute("product", product);
            context.setAttribute("originalProduct", cloneProduct(product));

            // 执行责任链处理
            ProductProcessor.ProcessResult result = processorChain.processProduct(context);

            // 获取处理后的产品数据
            TzProductDTO processedProduct = context.getAttribute("product");
            if (processedProduct == null) {
                log.warn("处理后的产品数据为空，使用原始产品数据");
                processedProduct = product;
            }

            // 记录处理结果
            long duration = System.currentTimeMillis() - startTime;
            if (result.isSuccess()) {
                successProcessCount.incrementAndGet();
                log.debug("产品策略处理成功: 产品ID={}, 耗时={}ms", context.getProductId(), duration);
            } else {
                failureProcessCount.incrementAndGet();
                log.warn("产品策略处理失败: 产品ID={}, 原因={}", context.getProductId(), result.getMessage());
            }

            // 记录性能信息
            context.setAttribute("processingDuration", duration);
            context.setAttribute("processingSuccess", result.isSuccess());

            return processedProduct;

        } catch (Exception e) {
            failureProcessCount.incrementAndGet();
            log.error("产品策略处理异常: 产品ID={}", context.getProductId(), e);
            // 异常时返回原始产品
            return product;
        }
    }

    @Override
    public TzProductDTO processProduct(TzProductDTO product) {
        if (product == null) {
            log.warn("产品数据为空");
            return null;
        }

        try {
            // 记录处理开始
            totalProcessCount.incrementAndGet();
            long startTime = System.currentTimeMillis();

            log.debug("开始简化产品策略处理: 产品ID={}", product.getId());

            // 构建最简化的处理上下文 - 包含基本信息和必要的上下文
            ProductProcessingContext.ProductProcessingContextBuilder contextBuilder = ProductProcessingContext.builder()
                .productId(product.getId())
                .processingStartTime(LocalDateTime.now())
                .processingMode(ProductProcessingContext.ProcessingMode.SIMPLIFIED);

            // 尝试获取用户ID（如果已登录）
            Long userId = UserContextHolder.getUserId();
            if (userId != null) {
                contextBuilder.userId(userId);
                log.debug("简化模式: 检测到已登录用户: {}", userId);
            }

            // 尝试获取租户ID（优先使用 EnhancedTenantContextHolder）
            String tenantId = EnhancedTenantContextHolder.getCurrentTenantId();

            if (tenantId != null) {
                contextBuilder.tenantId(tenantId);

                // 构建租户上下文（用于租户策略）
                TenantContext tenantContext = buildTenantContext(tenantId);
                contextBuilder.tenantContext(tenantContext);

                log.debug("简化模式: 构建租户上下文完成: {}", tenantId);
            } else {
                log.debug("简化模式: 未检测到租户ID");
            }

            ProductProcessingContext context = contextBuilder.build();

            // 将产品数据放入上下文
            context.setAttribute("product", product);
            context.setAttribute("originalProduct", product);

            // 执行责任链处理
            ProductProcessor.ProcessResult result = processorChain.processProduct(context);

            // 获取处理后的产品数据
            TzProductDTO processedProduct = context.getAttribute("product");
            if (processedProduct == null) {
                log.warn("处理后的产品数据为空，使用原始产品数据");
                processedProduct = product;
            }

            // 记录处理结果
            long duration = System.currentTimeMillis() - startTime;
            if (result.isSuccess()) {
                successProcessCount.incrementAndGet();
                log.debug("简化产品策略处理成功: 产品ID={}, 耗时={}ms", product.getId(), duration);
            } else {
                failureProcessCount.incrementAndGet();
                log.warn("简化产品策略处理失败: 产品ID={}, 原因={}", product.getId(), result.getMessage());
            }

            return processedProduct;

        } catch (Exception e) {
            failureProcessCount.incrementAndGet();
            log.error("简化产品策略处理异常: 产品ID={}", product.getId(), e);
            // 异常时返回原始产品
            return product;
        }
    }

    @Override
    @Deprecated
    public TzProductDTO processProduct(TzProductDTO product, Long userId, String tenantId, String platformProductId) {
        // 兼容性方法，内部调用简化的方法
        log.debug("使用已废弃的方法调用，建议升级到 processProduct(TzProductDTO product)");
        return processProduct(product);
    }

    @Override
    public List<TzProductDTO> processProduct(List<TzProductDTO> products) {
        if (products == null || products.isEmpty()) {
            return products;
        }

        // 获取智能定价策略
        PricingStrategy pricingStrategy = strategyFactory.getSmartPricingStrategy();
        if (pricingStrategy == null) {
            log.warn("未找到适用的定价策略，跳过批量定价");
            return products;
        }

        log.info("开始批量定价: 策略={}, 产品数量={}", pricingStrategy.getStrategyName(), products.size());

        int successCount = 0;
        for (TzProductDTO product : products) {
            try {
                ProductProcessingContext context = ProductProcessingContext.builder()
                    .productId(product.getId())
                    .platformProductId(product.getPdcPlatformProductId())
                    .userId(UserContextHolder.getUserId())
                    .tenantId(EnhancedTenantContextHolder.getCurrentTenantId())
                    .build();

                TzProductDTO processedProduct = pricingStrategy.process(product, context);
                if (processedProduct != null) {
                    successCount++;
                }
            } catch (Exception e) {
                log.error("批量定价异常: 产品ID={}", product.getId(), e);
            }
        }

        log.info("批量定价完成: 成功={}/{}", successCount, products.size());

        return products;
    }

    @Override
    public List<ProductInfoDTO> processProductInfoList(List<ProductInfoDTO> productInfoList) {
        if (productInfoList == null || productInfoList.isEmpty()) {
            return productInfoList;
        }

        try {
            // 🔧 关键修复：对输入列表进行深拷贝，避免对象引用污染
            log.debug("开始深拷贝ProductInfoDTO列表，避免对象引用污染，数量: {}", productInfoList.size());
            List<ProductInfoDTO> copiedProductInfoList = productInfoList.stream()
                .map(ProductInfoDTO::deepCopy)
                .toList();

            // 获取当前用户和租户信息
            Long userId = UserContextHolder.getUserId();
            String tenantId = EnhancedTenantContextHolder.getCurrentTenantId();

            // 构建轻量级用户定价上下文
            UserPricingContext userContext = userId != null ? buildUserPricingContext(userId) : null;
            TenantContext tenantContext = tenantId != null ? buildTenantContext(tenantId) : null;

            // 获取智能定价策略
            ProductProcessingContext dummyContext = ProductProcessingContext.builder()
                .userId(userId)
                .tenantId(tenantId)
                .userPricingContext(userContext)
                .tenantContext(tenantContext)
                .processingMode(ProductProcessingContext.ProcessingMode.LIGHTWEIGHT)
                .build();

            PricingStrategy pricingStrategy = strategyFactory.getBestPricingStrategy(dummyContext);
            if (pricingStrategy == null) {
                log.debug("未找到适用的定价策略，跳过轻量级批量价格处理");
                return copiedProductInfoList; // 返回深拷贝的列表，确保数据隔离
            }

            log.debug("开始轻量级批量价格处理: 策略={}, 产品数量={}",
                pricingStrategy.getStrategyName(), copiedProductInfoList.size());

            int successCount = 0;
            // 🔧 关键修复：在深拷贝的对象上进行价格策略处理
            for (ProductInfoDTO productInfo : copiedProductInfoList) {
                BigDecimal originPrice = productInfo.getPrice();
                log.debug("开始处理产品价格: 产品ID={} , 产品名称={} ，原价格={}", productInfo.getId(), productInfo.getName(), originPrice);
                try {
                    // 轻量级价格策略应用 - 仅更新价格相关字段
                    if (userContext != null && userContext.getMarkupRate() != null) {
                        // 应用用户加价率
                        if (productInfo.getPrice() != null) {
                            BigDecimal newPrice = productInfo.getPrice()
                                .multiply(BigDecimal.ONE.add(userContext.getMarkupRate()))
                                .setScale(2, RoundingMode.HALF_UP);
                            productInfo.setPrice(newPrice);
                            // 同时更新USD价格
                            productInfo.setUsdPrice(ProductBaseConvertMapping.INSTANCE.calculateUsdPrice(newPrice));
                        }

                        successCount++;
                    } else if (tenantContext != null && tenantContext.getDefaultMarkupRate() != null) {
                        // 降级到租户加价率
                        if (productInfo.getPrice() != null) {
                            BigDecimal newPrice = productInfo.getPrice()
                                .multiply(BigDecimal.ONE.add(tenantContext.getDefaultMarkupRate()))
                                .setScale(2, RoundingMode.HALF_UP);
                            productInfo.setPrice(newPrice);
                            // 同时更新USD价格
                            productInfo.setUsdPrice(ProductBaseConvertMapping.INSTANCE.calculateUsdPrice(newPrice));
                        }

                        successCount++;
                    } else {
                        // 使用默认的15%加价率
                        BigDecimal defaultMarkupRate = new BigDecimal("0.15");
                        if (productInfo.getPrice() != null) {
                            BigDecimal newPrice = productInfo.getPrice()
                                .multiply(BigDecimal.ONE.add(defaultMarkupRate))
                                .setScale(2, RoundingMode.HALF_UP);
                            productInfo.setPrice(newPrice);
                            // 同时更新USD价格
                            productInfo.setUsdPrice(ProductBaseConvertMapping.INSTANCE.calculateUsdPrice(newPrice));
                        }

                        successCount++;
                    }

                } catch (Exception e) {
                    log.error("轻量级价格处理异常: 产品ID={}", productInfo.getId(), e);
                }
                if (originPrice != null && productInfo.getPrice() != null) {
                    log.debug("处理产品价格完成: 产品ID={} ，原价格={} ，新价格={} ，价格费率={}",
                        productInfo.getId(), originPrice, productInfo.getPrice(),
                        productInfo.getPrice().divide(originPrice, RoundingMode.HALF_UP));
                }
            }

            log.debug("轻量级批量价格处理完成: 成功={}/{}", successCount, copiedProductInfoList.size());
            return copiedProductInfoList; // 返回处理后的深拷贝列表

        } catch (Exception e) {
            log.error("轻量级批量价格处理整体异常", e);
            // 异常时也返回深拷贝的原始数据，确保数据隔离
            return productInfoList.stream()
                .map(ProductInfoDTO::deepCopy)
                .collect(java.util.stream.Collectors.toList());
        }
    }

    @Override
    public TzProductDTO applyPricingStrategy(TzProductDTO product, UserPricingContext userContext) {
        if (product == null || userContext == null) {
            log.warn("定价策略应用: 产品数据或用户上下文为空");
            return product;
        }

        try {
            // 构建简化的处理上下文
            ProductProcessingContext context = ProductProcessingContext.builder()
                .userId(userContext.getUserId())
                .userPricingContext(userContext)
                .productId(product.getId())
                .build();

            // 获取最佳定价策略
            PricingStrategy pricingStrategy = strategyFactory.getBestPricingStrategy(context);
            if (pricingStrategy == null) {
                log.debug("没有找到适用的定价策略，返回原始产品");
                return product;
            }

            // 应用定价策略
            TzProductDTO processedProduct = pricingStrategy.process(product, context);

            log.debug("定价策略应用完成: 策略={}, 用户ID={}",
                pricingStrategy.getStrategyName(), userContext.getUserId());

            return processedProduct != null ? processedProduct : product;

        } catch (Exception e) {
            log.error("定价策略应用异常: 用户ID={}", userContext.getUserId(), e);
            return product;
        }
    }

    @Override
    public TzProductDTO applyPricingStrategy(TzProductDTO product, UserPricingContext userContext, TenantContext tenantContext) {
        if (product == null || userContext == null || tenantContext == null) {
            log.warn("定价策略应用: 产品数据、用户上下文或租户上下文为空");
            return product;
        }

        try {
            // 构建包含租户信息的处理上下文
            ProductProcessingContext context = ProductProcessingContext.builder()
                .userId(userContext.getUserId())
                .tenantId(tenantContext.getTenantId())
                .userPricingContext(userContext)
                .tenantContext(tenantContext)
                .productId(product.getId())
                .build();

            // 获取最佳定价策略（可能会选择租户策略）
            PricingStrategy pricingStrategy = strategyFactory.getBestPricingStrategy(context);
            if (pricingStrategy == null) {
                log.debug("没有找到适用的定价策略，返回原始产品");
                return product;
            }

            // 应用定价策略
            TzProductDTO processedProduct = pricingStrategy.process(product, context);

            log.debug("租户定价策略应用完成: 策略={}, 用户ID={}, 租户ID={}",
                pricingStrategy.getStrategyName(), userContext.getUserId(), tenantContext.getTenantId());

            return processedProduct != null ? processedProduct : product;

        } catch (Exception e) {
            log.error("租户定价策略应用异常: 用户ID={}, 租户ID={}", userContext.getUserId(), tenantContext.getTenantId(), e);
            return product;
        }
    }

    @Override
    public ProductProcessingContext buildProcessingContext(Long userId, String tenantId, String platformProductId, Long productId) {
        try {
            // 构建用户定价上下文
            UserPricingContext userPricingContext = userId != null ? buildUserPricingContext(userId) : null;

            // 构建租户上下文
            TenantContext tenantContext = tenantId != null ? buildTenantContext(tenantId) : null;

            // 构建完整的处理上下文
            return ProductProcessingContext.builder()
                .userId(userId)
                .tenantId(tenantId)
                .platformProductId(platformProductId)
                .productId(productId)
                .userPricingContext(userPricingContext)
                .tenantContext(tenantContext)
                .processingStartTime(LocalDateTime.now())
                .processingMode(ProductProcessingContext.ProcessingMode.NORMAL)
                .build();

        } catch (Exception e) {
            log.error("构建处理上下文异常: userId={}, tenantId={}, platformProductId={}",
                userId, tenantId, platformProductId, e);

            // 返回最小化的上下文
            return ProductProcessingContext.builder()
                .userId(userId)
                .tenantId(tenantId)
                .platformProductId(platformProductId)
                .productId(productId)
                .processingStartTime(LocalDateTime.now())
                .build();
        }
    }

    @Override
    public UserPricingContext buildUserPricingContext(Long userId) {
        if (userId == null) {
            return null;
        }

        try {
            // 直接使用UserContextHolder获取统一的服务费率
            BigDecimal serviceRate = UserContextHolder.getServiceFeeRate();
            boolean hasContext = UserContextHolder.hasUserContext();

            log.debug("构建用户定价上下文: userId={}, hasContext={}, serviceRate={}",
                userId, hasContext, serviceRate);

            return UserPricingContext.builder()
                .userId(userId)
                .userLevel(UserPricingContext.UserLevel.NORMAL)
                .markupRate(serviceRate != null ? serviceRate : new BigDecimal("0.15"))
                .discountRate(BigDecimal.ONE)
                .isVip(false)
                .vipLevel(0)
                .totalPurchaseAmount(BigDecimal.ZERO)
                .totalOrderCount(0)
                .preferredCurrency("CNY")
                .build();

        } catch (Exception e) {
            log.error("构建用户定价上下文异常: userId={}", userId, e);
            return null;
        }
    }

    @Override
    public TenantContext buildTenantContext(String tenantId) {
        if (tenantId == null) {
            return null;
        }

        try {
            // 直接使用UserContextHolder获取统一的服务费率（包含租户降级逻辑）
            BigDecimal serviceRate = UserContextHolder.getServiceFeeRate();
            String tenantName = EnhancedTenantContextHolder.getCurrentTenantName();
            boolean isPlanExpired = EnhancedTenantContextHolder.isCurrentTenantPlanExpired();

            log.debug("构建租户上下文: tenantId={}, tenantName={}, serviceRate={}, isPlanExpired={}",
                tenantId, tenantName, serviceRate, isPlanExpired);

            return TenantContext.builder()
                .tenantId(tenantId)
                .tenantName(tenantName != null ? tenantName : "租户-" + tenantId)
                .tenantType(TenantContext.TenantType.STANDARD)
                .tenantLevel(isPlanExpired ? TenantContext.TenantLevel.BASIC : TenantContext.TenantLevel.PROFESSIONAL)
                .defaultMarkupRate(serviceRate != null ? serviceRate : new BigDecimal("0.15"))
                .minMarkupRate(new BigDecimal("0.05"))
                .maxMarkupRate(new BigDecimal("0.50"))
                .tenantDiscountRate(BigDecimal.ONE)
                .enablePersonalizedPricing(true)
                .enableDynamicPricing(false)
                .defaultCurrency("CNY")
                .build();

        } catch (Exception e) {
            log.error("构建租户上下文异常: tenantId={}", tenantId, e);
            // 返回基础租户上下文
            return TenantContext.builder()
                .tenantId(tenantId)
                .tenantName("租户-" + tenantId)
                .tenantType(TenantContext.TenantType.STANDARD)
                .tenantLevel(TenantContext.TenantLevel.BASIC)
                .defaultMarkupRate(new BigDecimal("0.15"))
                .minMarkupRate(new BigDecimal("0.05"))
                .maxMarkupRate(new BigDecimal("0.50"))
                .tenantDiscountRate(BigDecimal.ONE)
                .enablePersonalizedPricing(true)
                .enableDynamicPricing(false)
                .defaultCurrency("CNY")
                .build();
        }
    }

    @Override
    public String getSystemStatus() {
        StringBuilder status = new StringBuilder();
        status.append("产品策略系统状态:\n");
        status.append("  处理器链状态: ").append(processorChain.getHealthStatus()).append("\n");
        status.append("  ").append(processorChain.getProcessingStatistics()).append("\n");
        status.append("  策略工厂状态: ").append(strategyFactory.getFactoryStatus()).append("\n");

        // 添加处理器信息
        status.append("  已注册处理器: ").append(processorChain.getProcessorCount()).append("个\n");
        processorChain.getProcessorInfo().forEach(info -> status.append("    - ").append(info).append("\n"));

        // 添加策略信息
        strategyFactory.getRegistrationInfo().forEach((type, strategies) -> {
            status.append("  ").append(type.getDescription()).append(": ").append(strategies.size()).append("个\n");
            strategies.forEach(strategyName -> status.append("    - ").append(strategyName).append("\n"));
        });

        return status.toString();
    }

    @Override
    public String getExecutionStatistics() {
        int total = totalProcessCount.get();
        int success = successProcessCount.get();
        int failure = failureProcessCount.get();
        double successRate = total > 0 ? (double) success / total * 100 : 0;

        StringBuilder stats = new StringBuilder();
        stats.append("产品策略执行统计:\n");
        stats.append(String.format("  总处理次数: %d\n", total));
        stats.append(String.format("  成功次数: %d\n", success));
        stats.append(String.format("  失败次数: %d\n", failure));
        stats.append(String.format("  成功率: %.2f%%\n", successRate));

        // 添加策略使用统计
        stats.append("  策略使用统计:\n");
        strategyFactory.getUsageStatistics().forEach((strategyName, count) -> stats.append(String.format("    - %s: %d次\n", strategyName, count)));

        return stats.toString();
    }

    @Override
    public void clearStrategyCache() {
        strategyFactory.clearCache();
        log.info("策略缓存已清理");
    }

    @Override
    public void resetStatistics() {
        totalProcessCount.set(0);
        successProcessCount.set(0);
        failureProcessCount.set(0);

        processorChain.resetStatistics();
        strategyFactory.resetUsageStatistics();

        log.info("策略统计已重置");
    }

    /**
     * 克隆产品数据（简化实现）
     */
    private TzProductDTO cloneProduct(TzProductDTO product) {
        try {
            // 这里应该实现深度克隆，当前简化处理
            return ObjectUtil.clone(product);
        } catch (Exception e) {
            log.warn("克隆产品数据失败", e);
            return product;
        }
    }

    /**
     * 获取服务健康状态
     *
     * @return 健康状态
     */
    public boolean isHealthy() {
        return processorChain.getProcessorCount() > 0 &&
            strategyFactory.getStrategyCount(ProductStrategy.StrategyType.PRICING) > 0;
    }

    /**
     * 获取性能指标
     *
     * @return 性能指标字符串
     */
    public String getPerformanceMetrics() {
        int total = totalProcessCount.get();
        if (total == 0) {
            return "暂无性能数据";
        }

        double successRate = (double) successProcessCount.get() / total * 100;

        return String.format("性能指标: 总处理=%d, 成功率=%.2f%%, 处理器数=%d, 策略数=%d",
            total, successRate, processorChain.getProcessorCount(),
            strategyFactory.getStrategyCount(ProductStrategy.StrategyType.PRICING));
    }

    // 之前的ThreadLocal和数据标记方案都不是问题所在
    // 真正的问题是缓存机制缓存了已处理的价格数据
    // 解决方案应该在门面服务层面使用强制刷新缓存，确保获取原始数据
}
