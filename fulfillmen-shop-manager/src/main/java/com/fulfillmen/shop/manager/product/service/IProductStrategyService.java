/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.product.service;

import com.fulfillmen.shop.domain.dto.ProductInfoDTO;
import com.fulfillmen.shop.domain.dto.TzProductDTO;
import com.fulfillmen.shop.manager.product.context.ProductProcessingContext;
import com.fulfillmen.shop.manager.product.context.TenantContext;
import com.fulfillmen.shop.manager.product.context.UserPricingContext;
import java.util.List;

/**
 * 内部产品策略服务接口
 *
 * <pre>
 * 设计原则：
 * 1. 包级可见性 - 仅供ProductFacadeService内部使用
 * 2. 策略模式集成 - 统一产品处理策略
 * 3. 责任链整合 - 支持多策略链式处理
 * 4. 上下文驱动 - 基于上下文的智能策略选择
 *
 * 核心职责：
 * - 产品定价策略处理
 * - 库存策略处理
 * - 图片策略处理
 * - 策略组合和链式处理
 * - 上下文构建和管理
 *
 * 使用场景：
 * - 产品同步时的策略应用
 * - 用户请求产品时的个性化处理
 * - 批量产品处理的策略应用
 * - 产品数据的实时策略调整
 *
 * 与V1.0版本的区别：
 * - 包级可见性，避免外部直接访问
 * - 优化上下文构建逻辑
 * - 增强批量处理能力
 * - 统一异常处理机制
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/08/22
 * @since 2.1.0
 * @see com.fulfillmen.shop.manager.product.service.IProductFacadeService
 */
public interface IProductStrategyService {

    /**
     * 处理产品数据（完整流程）
     *
     * @param product 原始产品数据
     * @param context 产品处理上下文
     * @return 处理后的产品数据
     */
    TzProductDTO processProduct(TzProductDTO product, ProductProcessingContext context);

    /**
     * 处理产品数据（最简化接口）
     *
     * <pre>
     * 自动从上下文获取用户和租户信息，应用统一的定价策略：
     * 1. 已登录用户：使用用户个人服务费率
     * 2. 未登录用户：使用租户默认服务费率
     * 3. 优先级：用户服务费 > 租户服务费 > 系统默认(15%)
     * </pre>
     *
     * @param product 原始产品数据
     * @return 处理后的产品数据
     */
    TzProductDTO processProduct(TzProductDTO product);

    /**
     * 处理产品数据（简化接口）
     *
     * @param product           原始产品数据
     * @param userId            用户ID
     * @param tenantId          租户ID
     * @param platformProductId 平台产品ID
     * @return 处理后的产品数据
     * @deprecated 建议使用 processProduct(TzProductDTO product) 方法
     */
    @Deprecated
    TzProductDTO processProduct(TzProductDTO product, Long userId, String tenantId, String platformProductId);

    /**
     * 批量处理产品数据（简化接口）
     *
     * @param products 原始产品数据列表
     * @return 处理后的产品数据
     */
    List<TzProductDTO> processProduct(List<TzProductDTO> products);

    /**
     * 批量处理产品信息列表（轻量级接口）
     *
     * <p>专门用于处理ProductInfoDTO列表，仅应用价格策略，不进行重型数据转换。</p>
     * <p>适用于列表页面的价格策略处理，保持高性能。</p>
     *
     * <h3>处理特性：</h3>
     * <ul>
     * <li>轻量级处理 - 仅对现有数据应用价格策略</li>
     * <li>高性能 - 无额外数据库查询和同步</li>
     * <li>批量优化 - 统一应用价格策略，提升效率</li>
     * <li>原地修改 - 直接修改ProductInfoDTO的价格字段</li>
     * </ul>
     *
     * @param productInfoList 产品信息列表
     * @return 处理后的产品信息列表，已应用价格策略
     * @since 2.0.0
     */
    List<ProductInfoDTO> processProductInfoList(List<ProductInfoDTO> productInfoList);

    /**
     * 只应用定价策略
     *
     * @param product     产品数据
     * @param userContext 用户定价上下文
     * @return 处理后的产品数据
     */
    TzProductDTO applyPricingStrategy(TzProductDTO product, UserPricingContext userContext);

    /**
     * 应用定价策略（包含租户上下文）
     *
     * @param product       产品数据
     * @param userContext   用户定价上下文
     * @param tenantContext 租户上下文
     * @return 处理后的产品数据
     */
    TzProductDTO applyPricingStrategy(TzProductDTO product, UserPricingContext userContext, TenantContext tenantContext);

    /**
     * 构建产品处理上下文
     *
     * @param userId            用户ID
     * @param tenantId          租户ID
     * @param platformProductId 平台产品ID
     * @param productId         内部产品ID
     * @return 产品处理上下文
     */
    ProductProcessingContext buildProcessingContext(Long userId, String tenantId, String platformProductId, Long productId);

    /**
     * 构建用户定价上下文
     *
     * @param userId 用户ID
     * @return 用户定价上下文
     */
    UserPricingContext buildUserPricingContext(Long userId);

    /**
     * 构建租户上下文
     *
     * @param tenantId 租户ID
     * @return 租户上下文
     */
    TenantContext buildTenantContext(String tenantId);

    /**
     * 获取策略系统状态信息
     *
     * @return 状态信息
     */
    String getSystemStatus();

    /**
     * 获取策略执行统计
     *
     * @return 统计信息
     */
    String getExecutionStatistics();

    /**
     * 清理策略缓存
     */
    void clearStrategyCache();

    /**
     * 重置策略统计
     */
    void resetStatistics();
}
