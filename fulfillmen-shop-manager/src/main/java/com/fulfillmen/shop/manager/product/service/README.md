# ProductFacadeService 使用指南

## 🚀 概述

`ProductFacadeService` 是基于门面设计模式的产品服务统一入口，提供了完整的产品搜索、获取、缓存和同步功能。该服务集成了现有的 `PdcProductMappingRepository` 和产品处理责任链架构，支持多数据源、智能缓存和虚拟线程并发处理。

## ✨ 主要功能

### 1. 产品搜索功能
- **关键词搜索**：支持中文、英文关键词搜索
- **高级搜索**：价格范围、分类筛选、排序等
- **分页支持**：灵活的分页查询
- **智能缓存**：JetCache 二级缓存优化

### 2. 产品获取功能
- **单个获取**：通过 ID 获取产品详情
- **批量获取**：高效的批量产品查询
- **多数据源**：本地数据库 → 产品映射 → 缓存API → 实时API
- **自动降级**：数据源失败时自动切换

### 3. 缓存管理功能
- **缓存预热**：主动加载热点商品到缓存
- **缓存刷新**：更新过期的缓存数据
- **批量操作**：支持批量缓存管理
- **性能监控**：缓存命中率和响应时间统计

### 4. 同步功能
- **本地同步**：将最新数据同步到本地数据库
- **批量同步**：使用虚拟线程的高性能批量同步
- **异步处理**：不阻塞主业务流程
- **错误恢复**：同步失败时的重试机制

## 🔧 快速开始

### 基础搜索示例

```java
@Autowired
private com.fulfillmen.shop.manager.product.service.IProductFacadeService productFacadeService;

// 1. 简单关键词搜索
List<TzProductDTO> results = productFacadeService.searchProducts("iPhone");

// 2. 分页搜索
List<TzProductDTO> pagedResults = productFacadeService.searchProducts("手机", 1, 20);

// 3. 处理搜索结果
results.forEach(product -> {
    System.out.println("产品: " + product.getName() + ", 价格: " + 
        (product.getSkuList() != null && !product.getSkuList().isEmpty() 
            ? product.getSkuList().get(0).getPrice() : "无价格"));
});
```

### 高级搜索示例

```java
// 构建搜索选项
ProductSearchOptions searchOptions = ProductSearchOptions.builder()
    .keyword("电脑")
    .pageNum(1)
    .pageSize(10)
    .categoryId(1000L)
    .minPrice(new BigDecimal("2000"))
    .maxPrice(new BigDecimal("8000"))
    .sortBy("price")
    .sortOrder("ASC")
    .includeImages(true)
    .onlyInStock(true)
    .build();

// 执行高级搜索
List<TzProductDTO> advancedResults = productFacadeService.searchProducts(searchOptions);
```

### 产品获取示例

```java
// 单个产品获取
TzProductDTO product = productFacadeService.getProduct("12345");

// 批量产品获取
List<String> productIds = Arrays.asList("12345", "67890", "11111");
List<TzProductDTO> products = productFacadeService.getProducts(productIds);

// 使用自定义获取选项
ProductGetOptions options = ProductGetOptions.builder()
    .forceRefresh(true)
    .syncToLocal(true)
    .build();
TzProductDTO freshProduct = productFacadeService.getProduct("12345", options);
```

### 缓存管理示例

```java
// 单个产品缓存预热
boolean success = productFacadeService.warmUpCache("12345");

// 批量缓存预热
List<String> productIds = Arrays.asList("12345", "67890", "11111");
Map<String, Boolean> results = productFacadeService.warmUpCache(productIds);

// 缓存刷新
boolean refreshed = productFacadeService.refreshCache("12345");
```

### 同步功能示例

```java
// 单个产品同步到本地
boolean synced = productFacadeService.syncProductToLocal("12345");

// 批量产品同步
List<String> productIds = Arrays.asList("12345", "67890");
Map<String, Boolean> syncResults = productFacadeService.syncProductsToLocal(productIds);

// 异步产品同步
CompletableFuture<Boolean> future = productFacadeService.syncProductToLocalAsync("12345");
```

## 📋 API 参考

### 核心搜索方法

| 方法 | 描述 | 参数 | 返回值 |
|------|------|------|--------|
| `searchProducts(String keyword)` | 基础关键词搜索 | 搜索关键词 | 产品列表 |
| `searchProducts(String keyword, int pageNum, int pageSize)` | 分页搜索 | 关键词、页号、页大小 | 产品列表 |
| `searchProducts(ProductSearchOptions options)` | 高级搜索 | 搜索选项对象 | 产品列表 |

### 产品获取方法

| 方法 | 描述 | 参数 | 返回值 |
|------|------|------|--------|
| `getProduct(String productId)` | 获取单个产品 | 产品ID | 产品对象 |
| `getProduct(String productId, ProductIdType idType)` | 指定ID类型获取 | 产品ID、ID类型 | 产品对象 |
| `getProducts(List<String> productIds)` | 批量获取产品 | 产品ID列表 | 产品列表 |

### 缓存管理方法

| 方法 | 描述 | 参数 | 返回值 |
|------|------|------|--------|
| `warmUpCache(String productId)` | 单个缓存预热 | 产品ID | 成功标志 |
| `warmUpCache(List<String> productIds)` | 批量缓存预热 | 产品ID列表 | 成功结果映射 |
| `refreshCache(String productId)` | 缓存刷新 | 产品ID | 成功标志 |

## ⚙️ 配置选项

### ProductSearchOptions 配置

```java
ProductSearchOptions.builder()
    .keyword("搜索关键词")           // 搜索关键词
    .pageNum(1)                    // 页号（从1开始）
    .pageSize(20)                  // 每页大小
    .categoryId(1000L)             // 分类ID过滤
    .minPrice(new BigDecimal("100")) // 最小价格
    .maxPrice(new BigDecimal("500")) // 最大价格
    .sortBy("price")               // 排序字段
    .sortOrder("ASC")              // 排序方向
    .includeImages(true)           // 是否包含图片
    .onlyInStock(true)             // 仅搜索有库存商品
    .build();
```

### ProductGetOptions 配置

```java
ProductGetOptions.builder()
    .forceRefresh(true)            // 强制刷新缓存
    .syncToLocal(true)             // 同步到本地数据库
    .cacheStrategy(HYBRID)         // 缓存策略
    .timeout(Duration.ofSeconds(30)) // 超时时间
    .build();
```

## 🔍 错误处理

### 常见异常处理

```java
try {
    List<TzProductDTO> results = productFacadeService.searchProducts("iPhone");
    // 处理结果
} catch (BusinessExceptionI18n e) {
    // 业务异常处理
    log.error("业务异常: {}", e.getMessage());
} catch (Exception e) {
    // 其他异常处理
    log.error("系统异常", e);
}
```

### 结果验证

```java
List<TzProductDTO> results = productFacadeService.searchProducts("手机");

// 检查结果是否为空
if (results.isEmpty()) {
    log.info("没有找到相关产品");
    return;
}

// 验证产品数据完整性
for (TzProductDTO product : results) {
    if (product.getId() == null) {
        log.warn("产品ID为空: {}", product);
        continue;
    }
    // 处理有效产品
}
```

## 🚀 性能优化建议

### 1. 搜索优化
- 使用合适的页面大小（推荐10-50条）
- 避免过于宽泛的关键词
- 合理使用价格和分类过滤

### 2. 缓存优化
- 对热点商品进行预热
- 定期刷新过期缓存
- 监控缓存命中率

### 3. 批量操作优化
- 使用批量接口而非循环调用
- 控制批量大小（推荐50-200条）
- 利用虚拟线程并发处理

### 4. 监控和调优
```java
// 获取性能报告
ProductSourcePerformanceReport report = productFacadeService.getPerformanceReport();

// 检查处理器状态
List<ProcessorStatus> statuses = productFacadeService.getProcessorStatus();
```

## 🧪 测试指南

项目包含完整的测试用例，包括：

- **单元测试**：`ProductFacadeServiceImplTest`
- **集成测试**：`ProductFacadeServiceIntegrationTest`
- **功能演示**：`ProductFacadeServiceDemo`

运行测试：
```bash
# 运行单元测试
mvnd test -pl fulfillmen-shop-manager -Dtest=ProductFacadeServiceImplTest

# 运行集成测试
mvnd test -pl fulfillmen-shop-manager -Dtest=ProductFacadeServiceIntegrationTest
```

## 📊 架构说明

### 数据流图
```
用户请求 → ProductFacadeService → ProductRetrievalProcessor → [LocalDB → Mapping → CachedAPI → LiveAPI]
                    ↓
         ProductSearchOptions → AggregateSearchReq → PdcProductMappingRepository
                    ↓
         PageDTO<ProductInfoDTO> → List<TzProductDTO> → 返回给用户
```

### 主要组件

- **ProductFacadeService**：门面服务，统一入口
- **ProductRetrievalProcessor**：责任链处理器
- **PdcProductMappingRepository**：数据仓储服务
- **TzProductMapping**：数据转换器
- **JetCache**：二级缓存系统
- **VirtualThreadExecutor**：虚拟线程执行器

### 架构优化 (V2.1.0)

在V2.1.0版本中，我们对架构进行了重要优化：

#### 1. 包结构重构
- 所有facade相关类迁移到 `com.fulfillmen.shop.manager.product.service` 包下
- 统一服务层架构，提升代码组织清晰度

#### 2. 作用域优化
- `IProductStrategyService` 设为包级可见性，限制外部直接访问
- `IProductSyncService` 设为包级可见性，仅供内部服务使用
- `ProductGetOptions`、`PricingOptions` 等配置类设为包级私有

#### 3. 门面模式强化
- `IProductFacadeService` 作为唯一对外接口
- 内部服务通过门面服务统一暴露
- 避免外部直接操作内部服务实现

#### 4. 职责边界清晰
```
IProductFacadeService (对外门面层)
├── IProductSyncService (内部同步服务)
├── IProductStrategyService (内部策略服务)
├── ProductQueryBuilder (内部查询构建器)
└── 配置类 (ProductGetOptions、PricingOptions)
```

## 📝 更新日志

### v2.1.0 (2025-09-10)
- ✅ 架构重构：facade包迁移到service包
- ✅ 作用域优化：内部服务设为包级可见性
- ✅ 门面模式强化：统一对外接口
- ✅ 职责边界清晰化

### v1.0.0 (2025-09-05)
- ✅ 实现基础搜索功能
- ✅ 集成统一聚合搜索
- ✅ 添加高级搜索选项
- ✅ 支持多数据源获取
- ✅ 集成JetCache缓存
- ✅ 虚拟线程并发优化
- ✅ 完整测试覆盖

## 🤝 贡献指南

1. 遵循现有代码规范
2. 添加充分的测试用例
3. 更新相关文档
4. 提交前运行完整测试套件
5. 遵循架构约束：仅通过 `IProductFacadeService` 访问产品服务

## 📞 支持

如有问题或建议，请联系：<EMAIL>