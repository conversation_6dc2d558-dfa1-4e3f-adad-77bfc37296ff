/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.product.service;

import com.fulfillmen.shop.domain.dto.TzProductDTO;
import com.fulfillmen.shop.domain.dto.product.alibaba.AlibabaProductDetailDTO;
import com.fulfillmen.shop.manager.service.dto.BatchSyncResult;
import java.util.List;

/**
 * 内部产品同步服务接口
 *
 * <pre>
 * 设计原则：
 * 1. 包级可见性 - 仅供ProductFacadeService内部使用
 * 2. 简化接口 - 专注于产品同步核心功能，去除冗余方法
 * 3. 统一职责 - 专门负责产品数据的同步和获取逻辑
 * 4. 高内聚 - 封装复杂的同步逻辑，对外提供简洁API
 *
 * 核心功能：
 * - 单个产品同步和获取
 * - 批量产品同步处理
 * - 智能缓存和同步策略
 * - 产品详情数据获取
 *
 * 与废弃版本的区别：
 * - 简化接口方法，去除SKU相关的细粒度控制
 * - 移除价格相关方法，统一交由策略服务处理
 * - 专注于数据同步，不涉及业务策略逻辑
 * - 包级可见性，避免外部误用
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/9/10
 * @since 2.1.0
 * @see com.fulfillmen.shop.manager.product.service.IProductFacadeService
 */
public interface IProductSyncService {

    // ==================== 核心同步方法 ====================

    /**
     * 同步单个产品数据
     *
     * <p>
     * 从外部平台同步产品数据到本地数据库，不应用任何业务策略。
     * 如果产品已存在则跳过同步，如需强制更新请使用 resyncProduct 方法。
     * </p>
     *
     * @param platformProductId 平台产品ID（如1688的offerId）
     * @return 同步后的原始产品数据，失败返回null
     */
    TzProductDTO syncProduct(String platformProductId);

    /**
     * 强制重新同步产品数据
     *
     * <p>
     * 强制从外部平台重新获取产品数据并更新本地记录，忽略现有数据。
     * 适用于数据修复和强制刷新场景。
     * </p>
     *
     * @param platformProductId 平台产品ID
     * @param forceUpdate       是否强制更新已存在的数据
     * @return 重新同步后的原始产品数据
     */
    TzProductDTO resyncProduct(String platformProductId, boolean forceUpdate);

    /**
     * 批量同步产品数据
     *
     * <p>
     * 高性能批量同步处理，使用虚拟线程并发执行以提升性能。
     * 支持部分失败容错，单个产品失败不影响整体处理。
     * 自动去重处理，避免重复同步相同产品。
     * </p>
     *
     * @param platformProductIds 平台产品ID列表，自动去重
     * @param forceUpdate        是否强制更新已存在的数据
     * @return 批量同步结果，包含详细的成功/失败统计
     */
    BatchSyncResult batchSyncProducts(List<String> platformProductIds, boolean forceUpdate);

    // ==================== 智能获取方法 ====================

    /**
     * 智能获取或同步产品数据
     *
     * <p>
     * 核心获取方法，实现智能缓存和同步策略：
     * 1. 优先从本地TzProduct表获取数据
     * 2. 本地不存在时自动触发同步
     * 3. 支持数据时效性检查和自动刷新
     * 4. 返回原始产品数据，不应用任何策略
     * </p>
     *
     * @param productId 产品ID，支持平台ID或内部ID
     * @return 产品数据，未找到返回null
     */
    TzProductDTO getOrSyncProduct(String productId);

    /**
     * 智能获取或同步产品数据（支持强制同步控制）
     *
     * <p>
     * 提供对同步行为的精确控制：
     * - forceSync=false: 智能同步，仅在数据过期时同步
     * - forceSync=true: 强制同步，跳过缓存直接获取最新数据
     * </p>
     *
     * @param productId 产品ID
     * @param forceSync 是否强制同步，true-强制重新同步，false-智能同步
     * @return 产品数据，未找到返回null
     */
    TzProductDTO getOrSyncProduct(String productId, boolean forceSync);

    // ==================== 产品详情方法 ====================

    /**
     * 获取原始产品详情数据
     *
     * <p>
     * 获取产品的完整详情信息，包括图片、描述、规格等原始数据。
     * 支持缓存机制以提升性能，适用于产品详情页展示。
     * </p>
     *
     * @param productId    产品ID
     * @param forceRefresh 是否强制刷新缓存
     * @return 产品详情数据，未找到返回null
     */
    AlibabaProductDetailDTO getProductDetail(String productId, boolean forceRefresh);

    /**
     * 获取原始产品详情数据（使用缓存）
     *
     * <p>便捷方法，等价于 getProductDetail(productId, false)</p>
     *
     * @param productId 产品ID
     * @return 产品详情数据，未找到返回null
     */
    default AlibabaProductDetailDTO getProductDetail(String productId) {
        return getProductDetail(productId, false);
    }

    // ==================== 数据验证方法 ====================

    /**
     * 检查产品是否已同步到本地
     *
     * @param platformProductId 平台产品ID
     * @return true-已同步，false-未同步
     */
    boolean isProductSynced(String platformProductId);

    /**
     * 检查产品数据是否需要刷新
     *
     * <p>
     * 基于数据最后更新时间判断是否需要重新同步。
     * 默认超过3天的数据认为需要刷新。
     * </p>
     *
     * @param platformProductId 平台产品ID
     * @return true-需要刷新，false-数据仍然新鲜
     */
    boolean needsRefresh(String platformProductId);
}
