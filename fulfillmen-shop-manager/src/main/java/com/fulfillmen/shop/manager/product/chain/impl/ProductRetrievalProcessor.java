/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.product.chain.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fulfillmen.shop.dao.mapper.TzProductSpuMapper;
import com.fulfillmen.shop.domain.convert.TzProductMapping;
import com.fulfillmen.shop.domain.dto.TzProductDTO;
import com.fulfillmen.shop.domain.dto.product.alibaba.AlibabaProductDetailDTO;
import com.fulfillmen.shop.domain.entity.TzProductSku;
import com.fulfillmen.shop.domain.entity.TzProductSpu;
import com.fulfillmen.shop.domain.entity.enums.PlatformCodeEnum;
import com.fulfillmen.shop.manager.core.repository.PdcProductMappingRepository;
import com.fulfillmen.shop.manager.core.repository.TzProductSkuRepository;
import com.fulfillmen.shop.manager.product.chain.ProductProcessor;
import com.fulfillmen.shop.manager.product.context.ProductProcessingContext;
import com.fulfillmen.shop.manager.product.enums.ProductSourceType;
import com.fulfillmen.shop.manager.product.service.ProductGetOptions;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

/**
 * 产品获取处理器 - 集成多数据源的完整实现
 *
 * <pre>
 * 基于现有ProductProcessor架构的产品数据获取处理器，支持完整的多数据源策略：
 *
 * 数据源优先级（按响应速度）：
 * 1. 本地数据库（TzProductSpu/TzProductSku）- 最快，50ms内响应
 * 2. 产品映射表（PdcProductMapping + 本地缓存）- 较快，150ms内响应
 * 3. 缓存API调用（优先使用缓存数据）- 中等，300ms内响应
 * 4. 实时API调用（强制刷新最新数据）- 最慢，800ms+响应
 *
 * 核心功能特性：
 * - 智能缓存策略：根据ProductGetOptions配置选择合适的数据源
 * - 性能监控：实时统计各数据源响应时间、成功率、调用次数
 * - 失败跟踪：记录每个数据源的失败原因，便于问题排查
 * - 调试支持：详细的执行日志记录，支持DEBUG模式
 * - 异步同步：支持数据同步到本地数据库，提升后续访问速度
 *
 * 集成现有架构：
 * - 使用统一的ProductProcessingContext作为上下文
 * - 完全遵循ProductProcessor的生命周期方法
 * - 支持责任链的继续执行（如价格策略、增强处理等）
 * - 与门面服务ProductFacadeService无缝集成
 *
 * 使用方式：
 * 1. 在ProductProcessingContext中设置产品ID和ProductGetOptions
 * 2. 处理器自动按优先级尝试各数据源
 * 3. 成功后将TzProductDTO存储到context的"product"属性中
 * 4. 性能数据和调试日志同时记录到context中
 *
 * 配置选项支持：
 * - forceRefresh: 强制跳过缓存，直接从API获取最新数据
 * - syncToLocal: 获取成功后异步同步到本地数据库
 * - 处理模式: NORMAL、FAST、DEBUG等不同处理策略
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/9/4
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ProductRetrievalProcessor extends ProductProcessor {

    private final TzProductSpuMapper tzProductSpuMapper;
    private final TzProductSkuRepository tzProductSkuRepository;
    private final PdcProductMappingRepository pdcProductMappingRepository;
    private final TransactionTemplate nayaTransactionTemplate;

    @Override
    protected boolean isApplicable(ProductProcessingContext context) {
        // 检查是否需要获取产品数据
        if (context.getAttribute("product") != null) {
            log.debug("产品数据已存在，跳过产品获取处理器");
            return false;
        }

        // 检查是否有产品ID
        String productId = getProductIdFromContext(context);
        if (productId == null || productId.trim().isEmpty()) {
            log.debug("产品ID为空，跳过产品获取处理器");
            return false;
        }

        return true;
    }

    @Override
    protected ProcessResult doProcess(ProductProcessingContext context) {
        try {
            String productId = getProductIdFromContext(context);
            ProductGetOptions options = getOptionsFromContext(context);

            context.addDebugLog("开始产品获取处理器执行: productId=" + productId);
            log.debug("开始获取产品数据: productId={}", productId);

            // 使用改进的产品获取逻辑
            TzProductDTO product = getOrSyncProductByPlatformId(productId, options, context);

            if (product == null) {
                String failureMsg = String.format("未能从任何数据源获取到产品数据: %s, 失败详情: %s",
                    productId, context.getSourceFailures());
                context.addDebugLog(failureMsg);
                return ProcessResult.failure(failureMsg);
            }

            // 将产品数据存储到上下文中
            context.setAttribute("product", product);
            context.setAttribute("productId", productId);

            String successMsg = String.format("产品数据获取成功: %s, 数据源: %s, %s",
                productId, context.getSuccessfulSource(), context.getPerformanceSummary());
            context.addDebugLog(successMsg);

            return ProcessResult.success(successMsg);

        } catch (Exception e) {
            String errorMsg = "产品获取处理器执行异常: " + e.getMessage();
            context.addDebugLog(errorMsg);
            log.error("产品获取处理器执行异常", e);
            return ProcessResult.failure(errorMsg, e);
        }
    }

    @Override
    protected String getProcessorName() {
        return "产品获取处理器";
    }

    @Override
    protected ProcessorType getProcessorType() {
        return ProcessorType.RETRIEVAL;
    }

    @Override
    protected int getOrder() {
        // 产品获取处理器应该在定价处理器之前执行
        return 10;
    }

    /**
     * 从上下文获取产品ID
     */
    private String getProductIdFromContext(ProductProcessingContext context) {
        // 尝试从多个可能的属性中获取产品ID
        String productId = context.getAttribute("productId");
        if (productId != null) {
            return productId;
        }

        productId = context.getPlatformProductId();
        if (productId != null) {
            return productId;
        }

        Long id = context.getProductId();
        if (id != null) {
            return id.toString();
        }

        return null;
    }

    /**
     * 从上下文获取获取选项
     */
    private ProductGetOptions getOptionsFromContext(ProductProcessingContext context) {
        ProductGetOptions options = context.getAttribute("getOptions");
        if (options != null) {
            return options;
        }

        // 如果没有指定选项，根据处理模式创建默认选项
        ProductProcessingContext.ProcessingMode mode = context.getProcessingMode();
        if (mode == ProductProcessingContext.ProcessingMode.SIMPLIFIED) {
            return ProductGetOptions.fastResponse();
        } else if (mode == ProductProcessingContext.ProcessingMode.DEBUG) {
            return ProductGetOptions.accurateData();
        }

        return ProductGetOptions.defaultOptions();
    }

    /**
     * 获取或同步产品数据 - 基于ProductSyncServiceImpl的核心逻辑
     *
     * @param platformProductId 平台产品ID
     * @param options           获取选项
     * @param context           处理上下文
     * @return 产品DTO，失败返回null
     */
    private TzProductDTO getOrSyncProductByPlatformId(String platformProductId, ProductGetOptions options,
        ProductProcessingContext context) {

        long startTime = System.currentTimeMillis();
        context.addDebugLog("开始获取或同步产品数据: platformProductId=" + platformProductId);

        try {
            // 1. 检查SPU是否存在
            TzProductSpu existingSpu = getExistingSpuByPlatformId(platformProductId, context);

            // 2. 检查是否需要重新同步
            boolean isNeedResync = needResync(existingSpu) || options.isForceRefresh();

            // 3. 如果SPU存在且不需要重新同步，则直接返回
            if (existingSpu != null && !isNeedResync) {
                context.addSuccess(ProductSourceType.LOCAL_DB, System.currentTimeMillis() - startTime);
                context.addDebugLog("从现有SPU获取产品数据: spuId=" + existingSpu.getId());
                return buildTzProductDtoFromSpu(existingSpu, platformProductId, context);
            }

            // 4. 从PdcProductMapping获取产品详情
            Long productId = Optional.ofNullable(existingSpu)
                .map(TzProductSpu::getPdcProductMappingId)
                .orElse(Long.valueOf(platformProductId));
            // 5. 从 PdcProductMapping 获取 或 调用 1688 OfferId API 获取产品详情
            AlibabaProductDetailDTO productDetail = pdcProductMappingRepository.getProductDetailWithCache(productId, isNeedResync);

            log.debug("产品详情获取成功: platformProductId={}", platformProductId);

            if (productDetail != null) {
                context.addSuccess(ProductSourceType.MAPPING, System.currentTimeMillis() - startTime);
                context.addDebugLog("从PdcProductMapping同步产品数据: platformProductId=" + platformProductId);
                return syncFromProductDetail(productDetail, existingSpu, options, context);
            }

            // 5. 数据获取失败
            context.addFailure(ProductSourceType.LIVE_API, "未找到产品数据");
            log.warn("获取或同步产品数据失败: platformProductId={}", platformProductId);
            return null;

        } catch (Exception e) {
            long responseTime = System.currentTimeMillis() - startTime;
            context.addFailure(ProductSourceType.LIVE_API, "获取异常: " + e.getMessage());
            log.error("获取或同步产品数据异常: platformProductId={}, 耗时={}ms", platformProductId, responseTime, e);
            return null;
        }
    }

    /**
     * 检查SPU是否已存在 - 基于ProductSyncServiceImpl的实现
     *
     * @param platformProductId 平台产品ID
     * @param context           处理上下文
     * @return SPU实体，不存在返回null
     */
    private TzProductSpu getExistingSpuByPlatformId(String platformProductId, ProductProcessingContext context) {
        context.addDebugLog("检查SPU是否已存在: platformProductId=" + platformProductId);

        try {
            // 1. 优先通过主键查询
            TzProductSpu productSpu = tzProductSpuMapper.selectById(platformProductId);
            if (productSpu != null) {
                context.addDebugLog("通过主键查询找到SPU: spuId=" + productSpu.getId());
                return productSpu;
            }

            // 2. 通过平台产品ID查询
            LambdaQueryWrapper<TzProductSpu> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(TzProductSpu::getPlatformCode, PlatformCodeEnum.PLATFORM_CODE_1688)
                .eq(TzProductSpu::getPdcPlatformProductId, platformProductId);
            productSpu = tzProductSpuMapper.selectOne(queryWrapper);

            if (productSpu != null) {
                context.addDebugLog("通过平台产品ID查询找到SPU: spuId=" + productSpu.getId());
                return productSpu;
            }

            // 3. 通过PdcProductMappingId查询
            if (platformProductId.matches("^\\d+$")) {
                queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(TzProductSpu::getPdcProductMappingId, Long.valueOf(platformProductId));
                productSpu = tzProductSpuMapper.selectOne(queryWrapper);

                if (productSpu != null) {
                    context.addDebugLog("通过PdcProductMappingId查询找到SPU: spuId=" + productSpu.getId());
                    return productSpu;
                }
            }

            context.addDebugLog("未找到现有SPU: platformProductId=" + platformProductId);
            return null;

        } catch (Exception e) {
            context.addDebugLog("查询现有SPU异常: " + e.getMessage());
            log.error("查询SPU失败: platformProductId={}", platformProductId, e);
            return null;
        }
    }

    /**
     * 检查TzProductSpu是否需要重新同步
     *
     * @param spu SPU实体
     * @return true-需要重新同步，false-数据仍然有效
     */
    private boolean needResync(TzProductSpu spu) {
        if (spu == null || spu.getGmtModified() == null) {
            return true;
        }

        // 检查数据是否超过3天未更新
        LocalDateTime lastModified = spu.getGmtModified();
        LocalDateTime now = LocalDateTime.now();
        Duration timeSinceUpdate = Duration.between(lastModified, now);

        boolean shouldResync = timeSinceUpdate.toDays() >= 3;

        if (shouldResync) {
            log.debug("TzProductSpu数据已过期，需要重新同步: spuId={}, 最后更新时间={}, 距今{}天",
                spu.getId(), lastModified, timeSinceUpdate.toDays());
        }

        return shouldResync;
    }

    /**
     * 从现有SPU构建TzProductDTO
     */
    private TzProductDTO buildTzProductDtoFromSpu(TzProductSpu spu, String platformProductId,
        ProductProcessingContext context) {
        try {
            Long productId = Long.valueOf(platformProductId);
            List<TzProductSku> existingSkuList = getSkuListBySpuId(spu.getId());
            AlibabaProductDetailDTO productDetail = pdcProductMappingRepository
                .getProductDetailWithCache(productId, false);

            TzProductDTO productDTO = TzProductMapping.INSTANCE.toTzProductDTO(spu, existingSkuList, productDetail);
            context.addDebugLog("从现有SPU构建DTO成功: skuCount=" +
                (existingSkuList != null ? existingSkuList.size() : 0));
            return productDTO;

        } catch (Exception e) {
            context.addDebugLog("从SPU构建DTO失败: " + e.getMessage());
            log.error("从SPU构建DTO失败: spuId={}, platformProductId={}", spu.getId(), platformProductId, e);
            return null;
        }
    }

    /**
     * 从产品详情同步产品数据
     */
    private TzProductDTO syncFromProductDetail(AlibabaProductDetailDTO productDetail, TzProductSpu existingSpu,
        ProductGetOptions options, ProductProcessingContext context) {
        try {
            if (productDetail == null) {
                context.addDebugLog("产品详情为空，无法同步");
                return null;
            }

            context.addDebugLog("开始从产品详情同步数据: productId=" + productDetail.getPlatformProductId());

            // 如果存在现有SPU，执行更新逻辑
            if (existingSpu != null) {
                return updateExistingSpu(existingSpu, productDetail, options, context);
            } else {
                return createNewSpu(productDetail, options, context);
            }

        } catch (Exception e) {
            context.addDebugLog("从产品详情同步失败: " + e.getMessage());
            log.error("从产品详情同步失败: productId={}", productDetail != null ? productDetail.getId() : "null", e);
            return null;
        }
    }

    /**
     * 更新现有SPU（使用编程式事务确保SPU和SKU同步更新）
     */
    private TzProductDTO updateExistingSpu(TzProductSpu existingSpu, AlibabaProductDetailDTO productDetail,
        ProductGetOptions options, ProductProcessingContext context) {
        try {
            context.addDebugLog("更新现有SPU: spuId=" + existingSpu.getId());

            // 使用编程式事务确保SPU和SKU同步更新
            return nayaTransactionTemplate.execute(status -> {
                try {
                    // 步骤1：更新SPU数据
                    TzProductSpu updatedSpu = TzProductMapping.INSTANCE.toTzProductSpu(productDetail);

                    // 保留现有ID和关键字段
                    existingSpu.setTitle(updatedSpu.getTitle());
                    existingSpu.setTitleTrans(updatedSpu.getTitleTrans());
                    existingSpu.setDescription(updatedSpu.getDescription());
                    existingSpu.setName(updatedSpu.getName());
                    existingSpu.setCategoryId(updatedSpu.getCategoryId());
                    existingSpu.setCategoryName(updatedSpu.getCategoryName());
                    existingSpu.setMainImage(updatedSpu.getMainImage());
                    existingSpu.setWhiteImage(updatedSpu.getWhiteImage());
                    existingSpu.setImages(updatedSpu.getImages());
                    existingSpu.setUnit(updatedSpu.getUnit());
                    existingSpu.setMinOrderQuantity(updatedSpu.getMinOrderQuantity());
                    existingSpu.setIsSingleItem(updatedSpu.getIsSingleItem());

                    // 执行SPU更新
                    int spuUpdateResult = tzProductSpuMapper.updateById(existingSpu);
                    if (spuUpdateResult <= 0) {
                        context.addDebugLog("SPU数据更新失败，可能是乐观锁冲突");
                        status.setRollbackOnly(); // 标记事务回滚
                        throw new RuntimeException("SPU数据更新失败，spuId=" + existingSpu.getId());
                    }

                    context.addDebugLog("SPU数据更新成功");

                    // 步骤2：同步更新SKU列表
                    List<TzProductSku> skuList = syncSkuListForExistingSpu(existingSpu.getId(), productDetail, context);

                    if (skuList == null || skuList.isEmpty()) {
                        context.addDebugLog("SKU列表同步失败");
                        status.setRollbackOnly(); // 标记事务回滚
                        throw new RuntimeException("SKU列表同步失败，spuId=" + existingSpu.getId());
                    }

                    // 步骤3：转换为DTO返回
                    TzProductDTO result = TzProductMapping.INSTANCE.toTzProductDTO(existingSpu, skuList, productDetail);
                    context.addDebugLog("SPU和SKU同步更新成功，SKU数量: " + skuList.size());
                    return result;

                } catch (Exception e) {
                    context.addDebugLog("事务内SPU/SKU更新失败: " + e.getMessage());
                    log.error("事务内SPU/SKU更新失败: spuId={}", existingSpu.getId(), e);
                    status.setRollbackOnly(); // 确保事务回滚
                    throw new RuntimeException("SPU/SKU同步更新失败", e);
                }
            });

        } catch (Exception e) {
            context.addDebugLog("更新现有SPU失败: " + e.getMessage());
            log.error("更新现有SPU失败: spuId={}", existingSpu.getId(), e);
            return null;
        }
    }

    /**
     * 创建新SPU
     */
    private TzProductDTO createNewSpu(AlibabaProductDetailDTO productDetail, ProductGetOptions options,
        ProductProcessingContext context) {
        try {
            context.addDebugLog("创建新SPU: productId=" + productDetail.getPlatformProductId());

            // 创建SPU
            TzProductSpu spu = TzProductMapping.INSTANCE.toTzProductSpu(productDetail);
            tzProductSpuMapper.insert(spu);
            context.addDebugLog("SPU创建成功: spuId=" + spu.getId());

            // 创建SKU列表
            List<TzProductSku> skuList = createSkuList(spu.getId(), productDetail, context);

            // 转换为DTO返回
            TzProductDTO result = TzProductMapping.INSTANCE.toTzProductDTO(spu, skuList, productDetail);
            context.addDebugLog("新SPU创建完成，SKU数量: " + (skuList != null ? skuList.size() : 0));
            return result;

        } catch (Exception e) {
            context.addDebugLog("创建新SPU失败: " + e.getMessage());
            log.error("创建新SPU失败: productId={}", productDetail.getPlatformProductId(), e);
            return null;
        }
    }

    /**
     * 获取现有SKU列表或创建新的
     */
    private List<TzProductSku> getOrCreateSkuList(Long spuId, AlibabaProductDetailDTO productDetail,
        ProductProcessingContext context) {

        List<TzProductSku> existingSkuList = getSkuListBySpuId(spuId);

        if (CollectionUtil.isNotEmpty(existingSkuList)) {
            context.addDebugLog("使用现有SKU列表: " + existingSkuList.size());
            return existingSkuList;
        }

        // 创建新的SKU列表
        return createSkuList(spuId, productDetail, context);
    }

    /**
     * 为现有SPU同步更新SKU列表（在事务中调用）
     * 
     * <pre>
     * SKU同步策略：
     * 1. 获取最新的产品SKU数据
     * 2. 比较现有SKU和新SKU的差异
     * 3. 更新现有SKU的价格、库存等信息
     * 4. 添加新的SKU（如果有）
     * 5. 标记删除的SKU（软删除，不物理删除）
     * </pre>
     */
    private List<TzProductSku> syncSkuListForExistingSpu(Long spuId, AlibabaProductDetailDTO productDetail,
        ProductProcessingContext context) {
        try {
            context.addDebugLog("开始同步SKU列表: spuId=" + spuId);

            // 获取现有SKU列表
            List<TzProductSku> existingSkuList = getSkuListBySpuId(spuId);

            if (CollectionUtil.isEmpty(existingSkuList)) {
                // 如果没有现有SKU，创建新的SKU列表
                context.addDebugLog("没有现有SKU，创建新的SKU列表");
                return createSkuList(spuId, productDetail, context);
            }

            // 如果产品详情中没有SKU信息，保持现有SKU不变
            if (productDetail.getProductSkuList() == null || productDetail.getProductSkuList().isEmpty()) {
                context.addDebugLog("产品详情中没有SKU信息，保持现有SKU: " + existingSkuList.size());
                return existingSkuList;
            }

            // 更新现有SKU的价格和库存信息
            boolean hasUpdates = false;
            for (TzProductSku existingSku : existingSkuList) {
                // 查找对应的新SKU数据
                var matchingNewSku = productDetail.getProductSkuList().stream()
                    .filter(newSku -> newSku.getCargoNumber() != null &&
                        newSku.getCargoNumber().equals(existingSku.getSku()))
                    .findFirst();

                if (matchingNewSku.isPresent()) {
                    var newSku = matchingNewSku.get();

                    boolean skuUpdated = false;

                    // 更新价格信息
                    if (newSku.getPrice() != null && !newSku.getPrice().equals(existingSku.getPrice())) {
                        existingSku.setPrice(newSku.getPrice());
                        skuUpdated = true;
                        context.addDebugLog("更新SKU价格: sku=" + existingSku.getSku() +
                            ", 新价格=" + newSku.getPrice());
                    }

                    // 更新库存信息
                    if (newSku.getAmountOnSale() != null && !newSku.getAmountOnSale().equals(existingSku.getQuantity())) {
                        existingSku.setQuantity(newSku.getAmountOnSale());
                        skuUpdated = true;
                        context.addDebugLog("更新SKU库存: sku=" + existingSku.getSku() +
                            ", 新库存=" + newSku.getAmountOnSale());
                    }

                    // 如果这个SKU有更新，执行数据库更新
                    if (skuUpdated) {
                        tzProductSkuRepository.updateById(existingSku);
                        hasUpdates = true;
                    }
                }
            }

            if (hasUpdates) {
                context.addDebugLog("SKU信息同步更新完成");
            } else {
                context.addDebugLog("SKU信息无变化，保持现有数据");
            }

            return existingSkuList;

        } catch (Exception e) {
            context.addDebugLog("SKU列表同步失败: " + e.getMessage());
            log.error("SKU列表同步失败: spuId={}", spuId, e);
            throw new RuntimeException("SKU列表同步失败", e);
        }
    }

    /**
     * 创建SKU列表
     */
    private List<TzProductSku> createSkuList(Long spuId, AlibabaProductDetailDTO productDetail,
        ProductProcessingContext context) {
        try {
            List<TzProductSku> skuList;

            if (!productDetail.isSingleItem() && productDetail.getProductSkuList() != null
                && !productDetail.getProductSkuList().isEmpty()) {

                // 多规格产品
                skuList = TzProductMapping.INSTANCE.toTzProductSkuList(
                    productDetail.getProductSkuList(),
                    spuId,
                    productDetail.getPlatformProductId(),
                    productDetail.getMinOrderQuantity()
                );

                // 批量插入SKU
                if (skuList != null && !skuList.isEmpty()) {
                    tzProductSkuRepository.batchInsertSkus(skuList);
                    context.addDebugLog("多规格SKU创建成功，数量: " + skuList.size());
                }

            } else {
                // 单品
                TzProductSku defaultSku = TzProductMapping.INSTANCE.toSingleItemSku(productDetail, spuId);
                if (defaultSku != null) {
                    tzProductSkuRepository.save(defaultSku);
                    skuList = List.of(defaultSku);
                    context.addDebugLog("单品默认SKU创建成功");
                } else {
                    skuList = List.of();
                }
            }

            return skuList;

        } catch (Exception e) {
            context.addDebugLog("创建SKU列表失败: " + e.getMessage());
            log.error("创建SKU列表失败: spuId={}", spuId, e);
            return List.of();
        }
    }

    /**
     * 根据SPU ID获取SKU列表
     */
    private List<TzProductSku> getSkuListBySpuId(Long spuId) {
        LambdaQueryWrapper<TzProductSku> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TzProductSku::getSpuId, spuId);
        return tzProductSkuRepository.list(queryWrapper);
    }

    /**
     * 异步同步到本地数据库
     */
    private void syncToLocalAsync(TzProductDTO product, ProductProcessingContext context) {
        // 标记需要同步，由上层服务处理
        context.setAttribute("needSyncToLocal", true);
        context.setAttribute("syncData", product);
    }

    /**
     * 从AlibabaProductDetailDTO中提取主图像
     *
     * <p>提取规则：
     * <ol>
     * <li>如果白底图存在，优先使用白底图作为主图</li>
     * <li>如果图片列表不为空，使用第一张图片</li>
     * <li>否则返回null</li>
     * </ol>
     *
     * @param productDetail 阿里巴巴产品详情
     * @return 主图像URL，可能为null
     */
    private String getMainImageFromDetail(AlibabaProductDetailDTO productDetail) {
        if (productDetail == null) {
            return null;
        }

        // 优先使用白底图
        if (productDetail.getWhiteImage() != null && !productDetail.getWhiteImage().trim().isEmpty()) {
            return productDetail.getWhiteImage();
        }

        // 其次使用图片列表的第一张图片
        if (productDetail.getImages() != null && !productDetail.getImages().isEmpty()) {
            return productDetail.getImages().getFirst();
        }

        return null;
    }

    /**
     * 将图片列表转换为字符串
     *
     * <p>转换规则：
     * <ul>
     * <li>如果列表为空或null，返回null</li>
     * <li>否则使用逗号分隔的字符串连接所有图片URL</li>
     * </ul>
     *
     * @param images 图片URL列表
     * @return 逗号分隔的图片URL字符串，可能为null
     */
    private String convertImagesToString(List<String> images) {
        if (images == null || images.isEmpty()) {
            return null;
        }
        return String.join(",", images);
    }
}
