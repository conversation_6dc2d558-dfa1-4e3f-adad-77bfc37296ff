/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.product.service.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.fulfillmen.shop.domain.dto.TzProductDTO;
import com.fulfillmen.shop.manager.product.service.IProductFacadeService;
import com.fulfillmen.shop.manager.product.service.ProductQueryBuilder;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

/**
 * 产品查询构建器实现类 - 内部实现
 *
 * <pre>
 * 作用域限制：
 * 1. 包级可见性 - 仅供product.service包内使用
 * 2. 通过IProductFacadeService的高级配置方法暴露
 * 3. 避免外部直接操作查询构建器实例
 *
 * 提供链式调用的产品查询功能，支持高级配置选项。
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/9/8
 * @since 2.0.0
 * @version 2.1.0 - 迁移到service包，限制作用域
 */
@Slf4j
public class ProductQueryBuilderImpl implements ProductQueryBuilder {

    private String productId;
    private boolean applyPricing = true; // 默认应用价格策略
    private boolean forceRefresh = false; // 默认使用缓存
    private long timeoutMs = 30000; // 默认30秒超时

    @Override
    public ProductQueryBuilder productId(String productId) {
        this.productId = productId;
        return this;
    }

    @Override
    public ProductQueryBuilder withPricing(boolean applyPricing) {
        this.applyPricing = applyPricing;
        return this;
    }

    @Override
    public ProductQueryBuilder forceRefresh(boolean forceRefresh) {
        this.forceRefresh = forceRefresh;
        return this;
    }

    @Override
    public ProductQueryBuilder timeout(long timeoutMs) {
        this.timeoutMs = timeoutMs;
        return this;
    }

    @Override
    public TzProductDTO execute() {
        // 参数校验
        if (!StringUtils.hasText(productId)) {
            throw new IllegalStateException("产品ID未设置，请先调用productId()方法");
        }

        log.debug("执行产品查询，ID: {}, 应用价格策略: {}, 强制刷新: {}", productId, applyPricing, forceRefresh);

        try {
            // 获取门面服务
            IProductFacadeService facadeService = getProductFacadeService();

            // 创建查询任务
            CompletableFuture<TzProductDTO> future = CompletableFuture.supplyAsync(() -> {
                if (forceRefresh) {
                    // 强制刷新：先同步再获取
                    facadeService.syncProduct(productId);
                }
                return facadeService.getProduct(productId, applyPricing);
            });

            // 应用超时设置
            TzProductDTO result = future.get(timeoutMs, TimeUnit.MILLISECONDS);

            log.debug("产品查询完成，ID: {}, 结果: {}", productId, result != null ? "成功" : "未找到");
            return result;

        } catch (TimeoutException e) {
            log.warn("产品查询超时，ID: {}, 超时时间: {}ms", productId, timeoutMs);
            throw new RuntimeException("产品查询超时", e);
        } catch (Exception e) {
            log.error("产品查询失败，ID: {}", productId, e);
            throw new RuntimeException("产品查询失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<TzProductDTO> executeAll(List<String> productIds) {
        // 参数校验
        if (productIds == null) {
            throw new IllegalArgumentException("产品ID列表不能为null");
        }

        if (productIds.isEmpty()) {
            return Collections.emptyList();
        }

        log.debug("执行批量产品查询，数量: {}, 应用价格策略: {}, 强制刷新: {}",
            productIds.size(), applyPricing, forceRefresh);

        try {
            // 获取门面服务
            IProductFacadeService facadeService = getProductFacadeService();

            // 创建批量查询任务
            CompletableFuture<List<TzProductDTO>> future = CompletableFuture.supplyAsync(() -> {
                if (forceRefresh) {
                    // 强制刷新：批量同步
                    productIds.forEach(id -> {
                        try {
                            facadeService.syncProduct(id);
                        } catch (Exception e) {
                            log.warn("同步产品失败，ID: {}", id, e);
                        }
                    });
                }
                return facadeService.getProducts(productIds, applyPricing);
            });

            // 应用超时设置
            List<TzProductDTO> results = future.get(timeoutMs, TimeUnit.MILLISECONDS);

            long successCount = results.stream().mapToLong(p -> p != null ? 1 : 0).sum();
            log.debug("批量产品查询完成，总数: {}, 成功: {}", productIds.size(), successCount);

            return results;

        } catch (TimeoutException e) {
            log.warn("批量产品查询超时，数量: {}, 超时时间: {}ms", productIds.size(), timeoutMs);
            throw new RuntimeException("批量产品查询超时", e);
        } catch (Exception e) {
            log.error("批量产品查询失败，数量: {}", productIds.size(), e);
            throw new RuntimeException("批量产品查询失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取产品门面服务实例
     */
    private IProductFacadeService getProductFacadeService() {
        try {
            return SpringUtil.getBean(IProductFacadeService.class);
        } catch (Exception e) {
            log.error("获取产品门面服务失败", e);
            throw new RuntimeException("获取产品门面服务失败", e);
        }
    }
}
