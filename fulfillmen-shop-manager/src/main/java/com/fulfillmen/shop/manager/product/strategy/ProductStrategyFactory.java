/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.product.strategy;

import com.fulfillmen.shop.common.context.UserContextHolder;
import com.fulfillmen.shop.common.tenant.EnhancedTenantContextHolder;
import com.fulfillmen.shop.manager.product.context.ProductProcessingContext;
import com.fulfillmen.shop.manager.product.strategy.pricing.PricingStrategy;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.EnumMap;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 产品策略工厂 - 内部使用
 *
 * <pre>
 * 作用域限制：
 * 1. 包级可见性 - 仅供product包内部使用
 * 2. 通过IProductStrategyService对外暴露功能
 * 3. 策略工厂的复杂逻辑不对外暴露
 *
 * 核心职责：
 * 1. 管理所有产品策略的注册和创建
 * 2. 根据上下文动态选择最适合的策略
 * 3. 提供策略的缓存和性能优化
 * 4. 支持策略的组合和链式使用
 *
 * 策略选择逻辑：
 * 1. 根据策略类型筛选候选策略
 * 2. 检查策略的适用性条件
 * 3. 按优先级排序选择最佳策略
 * 4. 支持多策略组合使用
 *
 * 工厂特性：
 * - 自动发现和注册Spring管理的策略Bean
 * - 策略选择结果缓存
 * - 策略执行统计和监控
 * - 支持策略的动态启用/禁用
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/08/22
 * @since 1.0.0
 * @see com.fulfillmen.shop.manager.product.service.IProductStrategyService
 */
@Slf4j
@Component
public class ProductStrategyFactory {

    private final Map<ProductStrategy.StrategyType, List<ProductStrategy<?>>> strategyRegistry;
    private final Map<String, ProductStrategy<?>> strategyByName;
    private final ConcurrentHashMap<String, String> strategySelectionCache;
    private final ConcurrentHashMap<String, Integer> strategyUsageStats;

    public ProductStrategyFactory(List<ProductStrategy<?>> allStrategies) {
        this.strategyRegistry = new EnumMap<>(ProductStrategy.StrategyType.class);
        this.strategyByName = new HashMap<>();
        this.strategySelectionCache = new ConcurrentHashMap<>();
        this.strategyUsageStats = new ConcurrentHashMap<>();

        this.registerStrategies(allStrategies);
        this.logRegisteredStrategies();
    }

    /**
     * 注册所有策略
     */
    private void registerStrategies(List<ProductStrategy<?>> strategies) {
        for (ProductStrategy<?> strategy : strategies) {
            // 按类型分组注册
            strategyRegistry.computeIfAbsent(strategy.getStrategyType(), k -> new ArrayList<>())
                .add(strategy);

            // 按名称注册
            strategyByName.put(strategy.getStrategyName(), strategy);

            log.debug("注册策略: {} (类型: {}, 顺序: {})",
                strategy.getStrategyName(), strategy.getStrategyType(), strategy.getOrder());
        }

        // 对每个类型的策略按顺序排序
        strategyRegistry.values().forEach(list -> list.sort(Comparator.comparing(ProductStrategy::getOrder)));
    }

    /**
     * 获取指定类型的最佳策略
     *
     * @param strategyType 策略类型
     * @param context      处理上下文
     * @param <T>          产品数据类型
     * @return 最佳策略，如果没有适用的策略则返回null
     */
    @SuppressWarnings("unchecked")
    public <T> ProductStrategy<T> getBestStrategy(ProductStrategy.StrategyType strategyType,
        ProductProcessingContext context) {
        if (context == null) {
            log.warn("获取策略失败：上下文为空");
            return null;
        }

        try {
            // 尝试从缓存获取
            String cacheKey = buildCacheKey(strategyType, context);
            String cachedStrategyName = strategySelectionCache.get(cacheKey);

            if (cachedStrategyName != null) {
                ProductStrategy<T> cachedStrategy = (ProductStrategy<T>) strategyByName.get(cachedStrategyName);
                if (cachedStrategy != null) {
                    log.debug("从缓存获取策略: {}", cachedStrategyName);
                    recordStrategyUsage(cachedStrategyName);
                    return cachedStrategy;
                }
            }

            // 缓存未命中，查找最佳策略
            List<ProductStrategy<?>> candidates = strategyRegistry.get(strategyType);
            if (candidates == null || candidates.isEmpty()) {
                log.debug("没有找到类型为 {} 的策略", strategyType);
                return null;
            }

            ProductStrategy<T> bestStrategy = findBestApplicableStrategy(candidates, context);

            if (bestStrategy != null) {
                // 缓存策略选择结果
                strategySelectionCache.put(cacheKey, bestStrategy.getStrategyName());
                recordStrategyUsage(bestStrategy.getStrategyName());

                log.debug("选择策略: {} (类型: {})", bestStrategy.getStrategyName(), strategyType);
            } else {
                log.debug("没有找到适用的 {} 策略", strategyType);
            }

            return bestStrategy;

        } catch (Exception e) {
            log.error("获取策略异常: 类型={}", strategyType, e);
            return null;
        }
    }

    /**
     * 获取所有适用的策略
     *
     * @param strategyType 策略类型
     * @param context      处理上下文
     * @param <T>          产品数据类型
     * @return 适用的策略列表
     */
    @SuppressWarnings("unchecked")
    public <T> List<ProductStrategy<T>> getApplicableStrategies(ProductStrategy.StrategyType strategyType,
        ProductProcessingContext context) {
        List<ProductStrategy<?>> candidates = strategyRegistry.get(strategyType);
        if (candidates == null || candidates.isEmpty()) {
            return Collections.emptyList();
        }

        return candidates.stream()
            .filter(strategy -> {
                try {
                    return strategy.isApplicable(context);
                } catch (Exception e) {
                    log.warn("检查策略适用性异常: {}", strategy.getStrategyName(), e);
                    return false;
                }
            })
            .map(strategy -> (ProductStrategy<T>) strategy)
            .sorted(Comparator.comparing(ProductStrategy::getOrder))
            .collect(Collectors.toList());
    }

    /**
     * 根据名称获取策略
     *
     * @param strategyName 策略名称
     * @param <T>          产品数据类型
     * @return 策略实例
     */
    @SuppressWarnings("unchecked")
    public <T> ProductStrategy<T> getStrategy(String strategyName) {
        return (ProductStrategy<T>) strategyByName.get(strategyName);
    }

    /**
     * 获取定价策略（便捷方法）
     * <p>
     * 策略选择逻辑： 1. 已登录用户 -> 使用用户定价策略 2. 未登录用户 -> 使用租户定价策略 3. 回退机制 -> 使用默认定价策略
     *
     * @param context 处理上下文
     * @return 最佳定价策略
     */
    public PricingStrategy getBestPricingStrategy(ProductProcessingContext context) {
        ProductStrategy<?> strategy = getBestStrategy(ProductStrategy.StrategyType.PRICING, context);
        if (strategy instanceof PricingStrategy) {
            log.debug("选择的定价策略: {}", strategy.getStrategyName());
            return (PricingStrategy) strategy;
        }

        // 如果没有找到适用的策略，记录警告
        log.warn("没有找到适用的定价策略，context: {}", context);
        return null;
    }

    /**
     * 根据用户登录状态智能选择定价策略
     *
     * @return 定价策略
     */
    public PricingStrategy getSmartPricingStrategy() {
        // 构建最小化的上下文用于策略选择
        ProductProcessingContext context = ProductProcessingContext.builder()
            .userId(getCurrentUserId())
            .tenantId(getCurrentTenantId())
            .build();

        return getBestPricingStrategy(context);
    }

    /**
     * 获取当前用户ID（辅助方法）
     */
    private Long getCurrentUserId() {
        try {
            return UserContextHolder.getUserId();
        } catch (Exception e) {
            log.debug("获取当前用户ID异常", e);
            return null;
        }
    }

    /**
     * 获取当前租户ID（辅助方法）
     */
    private String getCurrentTenantId() {
        try {
            return EnhancedTenantContextHolder.getCurrentTenantId();
        } catch (Exception e) {
            log.debug("获取当前租户ID异常", e);
            return null;
        }
    }

    /**
     * 查找最佳适用策略
     */
    @SuppressWarnings("unchecked")
    private <T> ProductStrategy<T> findBestApplicableStrategy(List<ProductStrategy<?>> candidates,
        ProductProcessingContext context) {
        return (ProductStrategy<T>) candidates.stream()
            .filter(strategy -> {
                try {
                    boolean applicable = strategy.isApplicable(context);
                    log.debug("策略适用性检查: {} = {}", strategy.getStrategyName(), applicable);
                    return applicable;
                } catch (Exception e) {
                    log.warn("策略适用性检查异常: {}", strategy.getStrategyName(), e);
                    return false;
                }
            })
            .min(Comparator.comparing(ProductStrategy::getOrder)).orElse(null);
    }

    /**
     * 构建缓存键
     */
    private String buildCacheKey(ProductStrategy.StrategyType strategyType, ProductProcessingContext context) {
        StringBuilder keyBuilder = new StringBuilder();
        keyBuilder.append(strategyType.name()).append(":");

        if (context.getUserId() != null) {
            keyBuilder.append("user:").append(context.getUserId()).append(":");
        }

        if (context.getTenantId() != null) {
            keyBuilder.append("tenant:").append(context.getTenantId()).append(":");
        }

        if (context.getUserPricingContext() != null) {
            var userContext = context.getUserPricingContext();
            if (userContext.isVip()) {
                keyBuilder.append("vip:").append(userContext.getVipLevel()).append(":");
            }
            keyBuilder.append("userLevel:").append(userContext.getUserLevel()).append(":");
        }

        return keyBuilder.toString();
    }

    /**
     * 记录策略使用统计
     */
    private void recordStrategyUsage(String strategyName) {
        strategyUsageStats.merge(strategyName, 1, Integer::sum);
    }

    /**
     * 打印已注册的策略
     */
    private void logRegisteredStrategies() {
        log.info("产品策略工厂初始化完成，已注册策略:");

        strategyRegistry.forEach((type, strategies) -> {
            log.info("  策略类型: {}", type.getDescription());
            strategies.forEach(strategy -> log.info("    - {} (顺序: {})", strategy.getStrategyName(), strategy.getOrder()));
        });
    }

    /**
     * 获取策略注册信息
     *
     * @return 注册信息映射
     */
    public Map<ProductStrategy.StrategyType, List<String>> getRegistrationInfo() {
        Map<ProductStrategy.StrategyType, List<String>> info = new EnumMap<>(ProductStrategy.StrategyType.class);

        strategyRegistry.forEach((type, strategies) -> {
            List<String> strategyNames = strategies.stream()
                .map(ProductStrategy::getStrategyName)
                .collect(Collectors.toList());
            info.put(type, strategyNames);
        });

        return info;
    }

    /**
     * 获取策略使用统计
     *
     * @return 使用统计信息
     */
    public Map<String, Integer> getUsageStatistics() {
        return new HashMap<>(strategyUsageStats);
    }

    /**
     * 清理缓存
     */
    public void clearCache() {
        int cacheSize = strategySelectionCache.size();
        strategySelectionCache.clear();
        log.info("策略选择缓存已清理，清理条目数: {}", cacheSize);
    }

    /**
     * 重置使用统计
     */
    public void resetUsageStatistics() {
        strategyUsageStats.clear();
        log.info("策略使用统计已重置");
    }

    /**
     * 获取工厂状态信息
     *
     * @return 状态信息字符串
     */
    public String getFactoryStatus() {
        int totalStrategies = strategyByName.size();
        int cacheSize = strategySelectionCache.size();
        int totalUsage = strategyUsageStats.values().stream().mapToInt(Integer::intValue).sum();

        return String.format("策略工厂状态: 总策略数=%d, 缓存条目=%d, 总使用次数=%d",
            totalStrategies, cacheSize, totalUsage);
    }

    /**
     * 检查策略是否已注册
     *
     * @param strategyName 策略名称
     * @return 是否已注册
     */
    public boolean isStrategyRegistered(String strategyName) {
        return strategyByName.containsKey(strategyName);
    }

    /**
     * 获取指定类型的策略数量
     *
     * @param strategyType 策略类型
     * @return 策略数量
     */
    public int getStrategyCount(ProductStrategy.StrategyType strategyType) {
        List<ProductStrategy<?>> strategies = strategyRegistry.get(strategyType);
        return strategies != null ? strategies.size() : 0;
    }

    /**
     * 获取所有策略类型
     *
     * @return 策略类型集合
     */
    public Set<ProductStrategy.StrategyType> getAvailableStrategyTypes() {
        return strategyRegistry.keySet();
    }
}
