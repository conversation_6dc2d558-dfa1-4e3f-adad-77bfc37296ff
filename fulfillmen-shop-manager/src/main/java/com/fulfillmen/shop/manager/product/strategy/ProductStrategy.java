/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.product.strategy;

import com.fulfillmen.shop.manager.product.context.ProductProcessingContext;
import lombok.Getter;

/**
 * 产品策略统一接口 - 内部使用
 *
 * <pre>
 * 作用域限制：
 * 1. 包级可见性 - 仅供product包内部使用
 * 2. 通过IProductStrategyService对外暴露功能
 * 3. 避免外部直接使用策略实现，确保架构清晰
 *
 * 核心职责：
 * 1. 定义产品处理策略的统一规范
 * 2. 支持策略的条件判断和执行顺序
 * 3. 提供类型安全的产品数据处理
 * 4. 支持策略组合和链式调用
 *
 * 设计原则：
 * - 单一职责：每个策略专注特定的产品处理逻辑
 * - 开闭原则：支持新策略的添加而无需修改现有代码
 * - 策略模式：运行时动态选择处理策略
 * - 封装性：通过服务层统一对外提供策略功能
 * </pre>
 *
 * @param <T> 产品数据类型（如TzProductDTO、AlibabaProductDetailDTO等）
 * <AUTHOR>
 * @date 2025/08/22
 * @since 1.0.0
 * @see com.fulfillmen.shop.manager.product.service.IProductStrategyService
 */
public interface ProductStrategy<T> {

    /**
     * 处理产品数据
     *
     * @param product 待处理的产品数据
     * @param context 处理上下文，包含用户、租户等信息
     * @return 处理后的产品数据
     */
    T process(T product, ProductProcessingContext context);

    /**
     * 判断策略是否适用于当前上下文
     *
     * @param context 处理上下文
     * @return true-适用该策略，false-跳过该策略
     */
    boolean isApplicable(ProductProcessingContext context);

    /**
     * 获取策略执行顺序
     *
     * <pre>
     * 数值越小越先执行：
     * - 10: 数据验证策略
     * - 20: 定价策略
     * - 30: 库存策略
     * - 40: 图片策略
     * - 50: 其他增强策略
     * </pre>
     *
     * @return 执行顺序，数值越小越先执行
     */
    int getOrder();

    /**
     * 获取策略名称
     *
     * @return 策略名称，用于日志和配置
     */
    String getStrategyName();

    /**
     * 策略类型枚举
     */
    @Getter
    enum StrategyType {

        PRICING("定价策略"),
        INVENTORY("库存策略"),
        IMAGE("图片策略"),
        VALIDATION("验证策略"),
        ENHANCEMENT("增强策略");

        private final String description;

        StrategyType(String description) {
            this.description = description;
        }

    }

    /**
     * 获取策略类型
     *
     * @return 策略类型
     */
    StrategyType getStrategyType();
}
