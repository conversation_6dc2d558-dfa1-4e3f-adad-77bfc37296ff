/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.product.service;

import com.fulfillmen.shop.domain.dto.TzProductDTO;
import com.fulfillmen.shop.manager.product.service.impl.ProductQueryBuilderImpl;
import java.util.List;

/**
 * 产品查询构建器接口 - 内部接口
 *
 * <pre>
 * 作用域限制：
 * 1. 包级可见性 - 仅供product.service包内使用
 * 2. 通过IProductFacadeService的高级配置方法暴露
 * 3. 避免外部直接操作查询构建器实例
 *
 * 提供链式调用体验，适用于需要复杂配置的高级查询场景。
 * 常规的简单查询建议直接使用 {@link IProductFacadeService} 的方法。
 *
 * 使用示例：
 * // 高级查询：强制刷新并应用价格策略
 * TzProductDTO product = ProductQueryBuilder.create()
 * .productId("648721093670")
 * .withPricing(true)
 * .forceRefresh(true)
 * .execute();
 *
 * // 批量查询：不应用价格策略，提升性能
 * List<TzProductDTO> products = ProductQueryBuilder.create()
 * .withPricing(false)
 * .executeAll(Arrays.asList("648721093670", "648721093671"));
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/9/8
 * @since 2.0.0
 * @version 2.1.0 - 迁移到service包，限制作用域
 */
public interface ProductQueryBuilder {

    /**
     * 创建查询构建器实例
     *
     * @return 新的查询构建器实例
     */
    static ProductQueryBuilder create() {
        return new ProductQueryBuilderImpl();
    }

    /**
     * 设置产品ID
     *
     * @param productId 产品ID，支持平台ID或内部ID
     * @return 当前构建器实例，支持链式调用
     */
    ProductQueryBuilder productId(String productId);

    /**
     * 设置是否应用价格策略
     *
     * @param applyPricing true-应用个性化价格，false-使用原始价格
     * @return 当前构建器实例，支持链式调用
     */
    ProductQueryBuilder withPricing(boolean applyPricing);

    /**
     * 设置是否强制刷新缓存
     *
     * @param forceRefresh true-强制从源头获取数据，false-使用缓存数据
     * @return 当前构建器实例，支持链式调用
     */
    ProductQueryBuilder forceRefresh(boolean forceRefresh);

    /**
     * 设置超时时间（毫秒）
     *
     * @param timeoutMs 超时时间，单位毫秒
     * @return 当前构建器实例，支持链式调用
     */
    ProductQueryBuilder timeout(long timeoutMs);

    /**
     * 执行单个产品查询
     *
     * @return 产品数据，未找到或查询失败返回null
     * @throws IllegalStateException 当未设置productId时抛出
     */
    TzProductDTO execute();

    /**
     * 批量执行产品查询
     *
     * @param productIds 产品ID列表
     * @return 产品列表，与输入顺序对应，未找到的产品为null
     * @throws IllegalArgumentException 当productIds为null或空时抛出
     */
    List<TzProductDTO> executeAll(List<String> productIds);
}
