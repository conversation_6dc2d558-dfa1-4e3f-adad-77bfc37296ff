/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.service.dto;

import com.fulfillmen.shop.domain.dto.TzProductDTO;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 批量同步结果DTO
 *
 * <pre>
 * 用于封装批量产品同步操作的结果信息：
 * 1. 成功同步的产品列表
 * 2. 失败同步的产品信息
 * 3. 统计信息和执行时间
 * 4. 详细的错误信息记录
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/8/27
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BatchSyncResult implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 总处理数量
     */
    private int totalCount;

    /**
     * 成功数量
     */
    private int successCount;

    /**
     * 失败数量
     */
    private int failureCount;

    /**
     * 跳过数量（重复或无效的ID）
     */
    private int skippedCount;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 执行耗时（毫秒）
     */
    private long executionTimeMs;

    /**
     * 成功同步的产品列表
     */
    @Builder.Default
    private List<TzProductDTO> successProducts = new ArrayList<>();

    /**
     * 失败的产品信息
     */
    @Builder.Default
    private List<FailedProduct> failedProducts = new ArrayList<>();

    /**
     * 跳过的产品ID列表
     */
    @Builder.Default
    private List<String> skippedProductIds = new ArrayList<>();

    /**
     * 是否全部成功
     */
    public boolean isAllSuccess() {
        return failureCount == 0 && skippedCount == 0;
    }

    /**
     * 获取成功率
     */
    public double getSuccessRate() {
        if (totalCount == 0) {
            return 0.0;
        }
        return (double) successCount / totalCount * 100;
    }

    /**
     * 创建空的批量同步结果
     *
     * @return 空的批量同步结果
     */
    public static BatchSyncResult createEmpty() {
        return BatchSyncResult.builder()
            .totalCount(0)
            .successCount(0)
            .failureCount(0)
            .skippedCount(0)
            .startTime(LocalDateTime.now())
            .endTime(LocalDateTime.now())
            .executionTimeMs(0L)
            .build();
    }

    /**
     * 从同步项列表创建批量同步结果
     *
     * @param syncItems       同步项列表
     * @param executionTimeMs 执行时间
     * @return 批量同步结果
     */
    public static BatchSyncResult fromSyncItems(List<SyncItem> syncItems, long executionTimeMs) {
        if (syncItems == null || syncItems.isEmpty()) {
            return createEmpty();
        }

        LocalDateTime now = LocalDateTime.now();
        List<TzProductDTO> successProducts = new ArrayList<>();
        List<FailedProduct> failedProducts = new ArrayList<>();

        int successCount = 0;
        int failureCount = 0;

        for (SyncItem item : syncItems) {
            if (item.isSuccess()) {
                successCount++;
                if (item.getProduct() != null) {
                    successProducts.add(item.getProduct());
                }
            } else {
                failureCount++;
                failedProducts.add(FailedProduct.builder()
                    .platformProductId(item.getPlatformProductId())
                    .errorMessage(item.getErrorMessage())
                    .failureTime(item.getSyncTime())
                    .build());
            }
        }

        return BatchSyncResult.builder()
            .totalCount(syncItems.size())
            .successCount(successCount)
            .failureCount(failureCount)
            .skippedCount(0)
            .startTime(now.minusNanos(executionTimeMs * 1_000_000L))
            .endTime(now)
            .executionTimeMs(executionTimeMs)
            .successProducts(successProducts)
            .failedProducts(failedProducts)
            .build();
    }

    /**
     * 失败的产品信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FailedProduct implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 平台产品ID
         */
        private String platformProductId;

        /**
         * 失败原因
         */
        private String errorMessage;

        /**
         * 异常类型
         */
        private String exceptionType;

        /**
         * 失败时间
         */
        private LocalDateTime failureTime;
    }

    /**
     * 单个同步项结果
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SyncItem implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 平台产品ID
         */
        private String platformProductId;

        /**
         * 是否成功
         */
        private boolean success;

        /**
         * 同步成功的产品数据（成功时非空）
         */
        private TzProductDTO product;

        /**
         * 错误消息（失败时非空）
         */
        private String errorMessage;

        /**
         * 同步时间
         */
        private LocalDateTime syncTime;

        /**
         * 创建成功的同步项
         *
         * @param productId 产品ID
         * @param product   产品数据
         * @return 成功的同步项
         */
        public static SyncItem success(String productId, TzProductDTO product) {
            return SyncItem.builder()
                .platformProductId(productId)
                .success(true)
                .product(product)
                .syncTime(LocalDateTime.now())
                .build();
        }

        /**
         * 创建失败的同步项
         *
         * @param productId    产品ID
         * @param errorMessage 错误消息
         * @return 失败的同步项
         */
        public static SyncItem failure(String productId, String errorMessage) {
            return SyncItem.builder()
                .platformProductId(productId)
                .success(false)
                .errorMessage(errorMessage)
                .syncTime(LocalDateTime.now())
                .build();
        }
    }
}
