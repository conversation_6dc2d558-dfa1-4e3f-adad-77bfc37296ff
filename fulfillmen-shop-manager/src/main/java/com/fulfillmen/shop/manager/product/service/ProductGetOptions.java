/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.product.service;

import com.fulfillmen.shop.manager.product.enums.ProductCacheStrategy;
import com.fulfillmen.shop.manager.product.enums.ProductSourceType;
import java.util.List;
import java.util.Set;
import lombok.Builder;
import lombok.Data;

/**
 * 产品获取选项配置 - 内部配置类
 *
 * <pre>
 * 作用域限制：
 * 1. 包级可见性 - 仅供product.service包内使用
 * 2. 通过IProductFacadeService的高级配置方法暴露
 * 3. 避免外部直接操作复杂的获取选项配置
 *
 * 简化的产品获取配置，仅保留核心选项：
 * 1. 数据刷新策略 - 是否强制刷新缓存
 * 2. 本地同步策略 - 是否同步到本地数据库
 * 3. 超时控制 - 请求超时时间
 * 4. 价格策略开关 - 是否启用价格策略
 * 5. 缓存策略 - 缓存使用策略
 *
 * 使用场景：
 * - 默认配置：适用于90%的使用场景
 * - 快速响应：高并发场景下的快速配置
 * - 准确数据：管理后台需要准确数据的配置
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/9/4
 * @since 1.0.0
 * @version 2.1.0 - 迁移到service包，限制作用域
 */
@Data
@Builder
public class ProductGetOptions {

    // ==================== 核心配置参数 (v2.0.0保留) ====================

    /**
     * 是否强制刷新数据
     * true: 跳过缓存，强制从源头获取最新数据
     * false: 根据缓存策略正常获取数据
     * 默认: false
     */
    @Builder.Default
    private boolean forceRefresh = false;

    /**
     * 是否同步数据到本地
     * true: 从远程获取的数据自动同步到本地数据库
     * false: 仅获取数据，不同步到本地
     * 默认: true
     */
    @Builder.Default
    private boolean syncToLocal = true;

    /**
     * 超时时间（毫秒）
     * 控制整个获取过程的最大等待时间
     * 默认: 5000ms (5秒)
     */
    @Builder.Default
    private int timeoutMs = 5000;

    /**
     * 是否启用价格策略处理
     * true: 对获取的产品数据应用价格策略
     * false: 返回原始价格，不进行价格策略处理
     * 默认: true
     */
    @Builder.Default
    private boolean enablePricing = true;

    /**
     * 缓存策略
     * 控制如何使用缓存和选择数据源
     * 默认: CACHE_FIRST（缓存优先）
     */
    @Builder.Default
    private ProductCacheStrategy cacheStrategy = ProductCacheStrategy.CACHE_FIRST;

    // ==================== 已弃用的配置参数 (v2.0.0标记废弃) ====================

    /**
     * 是否应用产品策略
     *
     * @deprecated 使用 {@link #enablePricing} 代替
     */
    @Builder.Default
    @Deprecated(since = "2.0.0", forRemoval = true)
    private boolean applyStrategies = true;

    /**
     * 排除的数据源
     *
     * @deprecated 数据源选择应该由底层系统自动优化
     */
    @Builder.Default
    @Deprecated(since = "2.0.0", forRemoval = true)
    private Set<ProductSourceType> excludeSources = Set.of();

    /**
     * 是否启用异步同步
     *
     * @deprecated 同步策略应该由底层系统自动处理
     */
    @Builder.Default
    @Deprecated(since = "2.0.0", forRemoval = true)
    private boolean asyncSync = false;

    /**
     * 是否记录性能指标
     *
     * @deprecated 性能指标应该通过监控系统自动收集
     */
    @Builder.Default
    @Deprecated(since = "2.0.0", forRemoval = true)
    private boolean recordMetrics = true;

    /**
     * 是否允许部分失败
     *
     * @deprecated 容错策略应该由底层系统自动处理
     */
    @Builder.Default
    @Deprecated(since = "2.0.0", forRemoval = true)
    private boolean allowPartialFailure = true;

    // ==================== 已弃用的价格策略配置 ====================

    /**
     * 首选的价格策略名称列表
     *
     * @deprecated 价格策略优先级应该通过配置文件或系统设置管理
     */
    @Builder.Default
    @Deprecated(since = "2.0.0", forRemoval = true)
    private List<String> preferredPricingStrategies = List.of();

    /**
     * 是否应用级联价格策略
     *
     * @deprecated 价格策略的级联逻辑应该由底层价格引擎处理
     */
    @Builder.Default
    @Deprecated(since = "2.0.0", forRemoval = true)
    private boolean applyCascadingStrategies = false;

    /**
     * 是否在价格策略失败时使用降级策略
     *
     * @deprecated 降级策略应该由底层价格引擎自动处理
     */
    @Builder.Default
    @Deprecated(since = "2.0.0", forRemoval = true)
    private boolean enablePricingFallback = true;

    /**
     * 是否记录价格策略执行日志
     *
     * @deprecated 价格策略日志应该通过日志系统统一管理
     */
    @Builder.Default
    @Deprecated(since = "2.0.0", forRemoval = true)
    private boolean logPricingDetails = false;

    // ==================== 简化的工厂方法 (v2.0.0) ====================

    /**
     * 创建默认配置
     * 适用于大部分前端用户场景，使用所有默认设置
     *
     * @return 默认产品获取选项
     */
    public static ProductGetOptions defaultOptions() {
        return ProductGetOptions.builder().build();
    }

    /**
     * 创建快速响应配置
     * 适用于高并发场景，追求响应速度，缓存优先
     *
     * @return 快速响应配置
     */
    public static ProductGetOptions fastResponse() {
        return ProductGetOptions.builder()
            .cacheStrategy(ProductCacheStrategy.CACHE_FIRST)
            .timeoutMs(2000)
            .enablePricing(true)
            .build();
    }

    /**
     * 创建准确数据配置
     * 适用于管理后台，要求数据准确性，强制刷新
     *
     * @return 准确数据配置
     */
    public static ProductGetOptions accurateData() {
        return ProductGetOptions.builder()
            .forceRefresh(true)
            .cacheStrategy(ProductCacheStrategy.API_FIRST)
            .syncToLocal(true)
            .timeoutMs(10000)
            .enablePricing(true)
            .build();
    }

    /**
     * 创建数据同步配置
     * 适用于后台数据同步任务，不处理价格策略
     *
     * @return 数据同步配置
     */
    static ProductGetOptions syncMode() {
        return ProductGetOptions.builder()
            .forceRefresh(true)
            .syncToLocal(true)
            .enablePricing(false)
            .cacheStrategy(ProductCacheStrategy.API_FIRST)
            .timeoutMs(30000)
            .build();
    }

    // ==================== 简化的便捷方法 (v2.0.0) ====================

    /**
     * 检查是否使用快速模式
     * 快速模式：缓存优先且超时时间较短
     *
     * @return 是否快速模式
     */
    boolean isFastMode() {
        return timeoutMs <= 3000 && cacheStrategy == ProductCacheStrategy.CACHE_FIRST;
    }

    /**
     * 检查是否使用严格模式
     * 严格模式：强制刷新且要求数据准确性
     *
     * @return 是否严格模式
     */
    boolean isStrictMode() {
        return forceRefresh && cacheStrategy == ProductCacheStrategy.API_FIRST;
    }

    /**
     * 检查是否启用了价格策略处理
     *
     * @return 是否启用价格策略
     */
    boolean isPricingEnabled() {
        return enablePricing;
    }

    /**
     * 检查是否为同步模式
     * 同步模式：强制刷新、本地同步且不处理价格
     *
     * @return 是否同步模式
     */
    boolean isSyncMode() {
        return forceRefresh && syncToLocal && !enablePricing;
    }
}