/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.product.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fulfillmen.shop.common.enums.FulfillmenErrorCodeEnum;
import com.fulfillmen.shop.common.exception.BusinessExceptionI18n;
import com.fulfillmen.shop.dao.mapper.TzProductSpuMapper;
import com.fulfillmen.shop.domain.convert.TzProductMapping;
import com.fulfillmen.shop.domain.dto.TzProductDTO;
import com.fulfillmen.shop.domain.dto.product.alibaba.AlibabaProductDetailDTO;
import com.fulfillmen.shop.domain.entity.TzProductSku;
import com.fulfillmen.shop.domain.entity.TzProductSpu;
import com.fulfillmen.shop.domain.entity.enums.PdcProductMappingSyncStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.PlatformCodeEnum;
import com.fulfillmen.shop.manager.core.repository.PdcProductMappingRepository;
import com.fulfillmen.shop.manager.core.repository.TzProductSkuRepository;
import com.fulfillmen.shop.manager.product.service.IProductSyncService;
import com.fulfillmen.shop.manager.service.dto.BatchSyncResult;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.StringUtils;

/**
 * 内部产品同步服务实现
 *
 * <pre>
 * 设计原则：
 * 1. 包级作用域 - 仅供ProductFacadeService内部使用
 * 2. 专注同步 - 不涉及价格策略等业务逻辑
 * 3. 高性能 - 使用虚拟线程和并发处理
 * 4. 容错性 - 完善的错误处理和降级机制
 *
 * 核心职责：
 * - 产品数据同步和获取
 * - 批量处理和并发优化
 * - 缓存策略和数据时效性管理
 * - 错误处理和异常恢复
 *
 * 实现特色：
 * - 复用原有同步逻辑，确保功能稳定性
 * - 简化接口设计，去除业务策略相关方法
 * - 优化批量处理性能，使用虚拟线程
 * - 统一错误处理，提供一致的异常信息
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/9/10
 * @since 2.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProductSyncServiceImpl implements IProductSyncService {

    private final PdcProductMappingRepository pdcProductMappingRepository;
    private final TzProductSkuRepository tzProductSkuRepository;
    private final TzProductSpuMapper tzProductSpuMapper;
    private final Executor virtualThreadExecutor;
    private final TransactionTemplate transactionTemplate;

    // ==================== 核心同步方法 ====================

    @Override
    public TzProductDTO syncProduct(String platformProductId) {
        validateProductId(platformProductId);

        log.debug("开始同步产品数据，platformProductId: {}", platformProductId);

        try {
            // 1. 检查SPU是否已存在（使用复杂的查询策略）
            TzProductSpu existingSpu = getExistingSpuByPlatformId(platformProductId);
            Long productId = Long.valueOf(platformProductId);

            // 异步获取产品详情以提升性能
            CompletableFuture<AlibabaProductDetailDTO> productDetailFuture = CompletableFuture
                .supplyAsync(() -> pdcProductMappingRepository.getProductDetailWithCache(productId, false),
                    virtualThreadExecutor);

            if (existingSpu != null) {
                log.debug("SPU已存在（基于平台产品ID），直接返回，spuId: {}", existingSpu.getId());
                // 获取现有的SKU列表
                List<TzProductSku> existingSkuList = getSkuListBySpuId(existingSpu.getId());
                // 如果不存在SKU，则创建一个
                if (CollectionUtil.isEmpty(existingSkuList)) {
                    log.warn("SKU列表为空，platformProductId: {}", platformProductId);
                    // 在事务中创建默认SKU
                    TzProductSku defaultSku = transactionTemplate.execute(status -> {
                        AlibabaProductDetailDTO detail = productDetailFuture.join();
                        return createDefaultSkuInTransaction(detail, existingSpu);
                    });
                    if (defaultSku != null) {
                        existingSkuList.add(defaultSku);
                        log.debug("单品默认SKU创建成功，skuId: {}", defaultSku.getId());
                    }
                }
                // 转换为DTO
                return TzProductMapping.INSTANCE.toTzProductDTO(existingSpu, existingSkuList, productDetailFuture.get());
            }

            // 2. 获取产品详情并创建新SPU
            AlibabaProductDetailDTO productDetail = productDetailFuture.get();
            if (productDetail == null) {
                log.warn("未找到产品详情，platformProductId: {}", platformProductId);
                return null;
            }

            // 使用编程式事务创建新产品
            return transactionTemplate.execute(status -> {
                try {
                    return createNewProductInTransaction(productDetail);
                } catch (Exception e) {
                    status.setRollbackOnly();
                    handleSyncException(e, platformProductId);
                    return null;
                }
            });

        } catch (Exception e) {
            handleSyncException(e, platformProductId);
            return null;
        }
    }

    @Override
    public TzProductDTO resyncProduct(String platformProductId, boolean forceUpdate) {
        validateProductId(platformProductId);

        log.debug("开始强制重新同步产品数据，platformProductId: {}, forceUpdate: {}",
            platformProductId, forceUpdate);

        try {
            Long productId = Long.valueOf(platformProductId);

            // 强制刷新获取最新产品详情
            AlibabaProductDetailDTO productDetail = pdcProductMappingRepository
                .getProductDetailWithCache(productId, true);

            if (productDetail == null) {
                log.warn("未找到产品详情，无法同步，platformProductId: {}", platformProductId);
                return null;
            }

            TzProductSpu existingSpu = getExistingSpuByPlatformId(platformProductId);

            if (existingSpu != null && forceUpdate) {
                // 强制更新模式：更新现有SPU
                return transactionTemplate.execute(status -> {
                    try {
                        return updateExistingProductInTransaction(existingSpu, productDetail);
                    } catch (Exception e) {
                        status.setRollbackOnly();
                        throw e;
                    }
                });
            } else if (existingSpu != null) {
                // 非强制更新：返回现有数据
                return buildProductDTOFromExisting(existingSpu, productDetail);
            } else {
                // 不存在：创建新产品
                return transactionTemplate.execute(status -> {
                    try {
                        return createNewProductInTransaction(productDetail);
                    } catch (Exception e) {
                        status.setRollbackOnly();
                        throw e;
                    }
                });
            }

        } catch (Exception e) {
            handleSyncException(e, platformProductId);
            return null;
        }
    }

    @Override
    public BatchSyncResult batchSyncProducts(List<String> platformProductIds, boolean forceUpdate) {
        // 参数校验和预处理
        if (platformProductIds == null) {
            return BatchSyncResult.createEmpty();
        }

        // 去重和过滤无效ID
        List<String> validIds = platformProductIds.stream()
            .filter(StringUtils::hasText)
            .distinct()
            .toList();

        if (validIds.isEmpty()) {
            return BatchSyncResult.createEmpty();
        }

        log.info("开始批量同步产品，总数: {}, 强制更新: {}", validIds.size(), forceUpdate);
        long startTime = System.currentTimeMillis();

        // 并发处理以提升性能
        List<CompletableFuture<BatchSyncResult.SyncItem>> futures = validIds.stream()
            .map(productId -> CompletableFuture.supplyAsync(
                () -> syncSingleProductForBatch(productId, forceUpdate),
                virtualThreadExecutor))
            .toList();

        // 等待所有任务完成并收集结果
        List<BatchSyncResult.SyncItem> results = futures.stream()
            .map(CompletableFuture::join)
            .collect(Collectors.toList());

        long executionTime = System.currentTimeMillis() - startTime;
        BatchSyncResult batchResult = BatchSyncResult.fromSyncItems(results, executionTime);

        log.info("批量同步产品完成，总数: {}, 成功: {}, 失败: {}, 跳过: {}, 耗时: {}ms",
            batchResult.getTotalCount(), batchResult.getSuccessCount(),
            batchResult.getFailureCount(), batchResult.getSkippedCount(), executionTime);

        return batchResult;
    }

    // ==================== 智能获取方法 ====================

    @Override
    public TzProductDTO getOrSyncProduct(String productId) {
        return getOrSyncProduct(productId, false);
    }

    @Override
    public TzProductDTO getOrSyncProduct(String productId, boolean forceSync) {
        validateProductId(productId);

        log.debug("智能获取或同步产品，productId: {}, forceSync: {}", productId, forceSync);

        try {
            // 先尝试从本地获取
            TzProductSpu existingSpu = getExistingSpuByPlatformId(productId);

            if (existingSpu != null) {
                // 检查是否需要刷新
                if (forceSync || needsRefresh(productId)) {
                    log.debug("产品数据需要刷新，执行重新同步，productId: {}", productId);
                    return resyncProduct(productId, true);
                } else {
                    log.debug("使用缓存的产品数据，productId: {}", productId);
                    return buildProductDTOFromExisting(existingSpu, getProductDetail(productId, false));
                }
            } else {
                // 本地不存在，执行同步
                log.debug("本地不存在产品数据，执行同步，productId: {}", productId);
                return syncProduct(productId);
            }

        } catch (Exception e) {
            log.error("智能获取或同步产品失败，productId: {}", productId, e);
            return null;
        }
    }

    // ==================== 产品详情方法 ====================

    @Override
    public AlibabaProductDetailDTO getProductDetail(String productId, boolean forceRefresh) {
        validateProductId(productId);

        try {
            Long id = Long.valueOf(productId);
            return pdcProductMappingRepository.getProductDetailWithCache(id, forceRefresh);
        } catch (Exception e) {
            log.error("获取产品详情失败，productId: {}", productId, e);
            return null;
        }
    }

    // ==================== 数据验证方法 ====================

    @Override
    public boolean isProductSynced(String platformProductId) {
        if (!StringUtils.hasText(platformProductId)) {
            return false;
        }

        try {
            return getExistingSpuByPlatformId(platformProductId) != null;
        } catch (Exception e) {
            log.error("检查产品同步状态失败，platformProductId: {}", platformProductId, e);
            return false;
        }
    }

    @Override
    public boolean needsRefresh(String platformProductId) {
        if (!StringUtils.hasText(platformProductId)) {
            return true;
        }

        try {
            TzProductSpu spu = getExistingSpuByPlatformId(platformProductId);
            if (spu == null) {
                return true;
            }

            // 检查数据是否超过2天
            LocalDateTime lastUpdate = spu.getGmtModified() != null ? spu.getGmtModified() : spu.getGmtCreated();
            Duration duration = Duration.between(lastUpdate, LocalDateTime.now());
            boolean needsRefresh = duration.toDays() >= 2;

            log.debug("产品数据时效检查，platformProductId: {}, 最后更新: {}, 需要刷新: {}",
                platformProductId, lastUpdate, needsRefresh);

            return needsRefresh;

        } catch (Exception e) {
            log.error("检查产品刷新需求失败，platformProductId: {}", platformProductId, e);
            return true;
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 参数校验
     */
    private void validateProductId(String productId) {
        if (!StringUtils.hasText(productId)) {
            throw new IllegalArgumentException("产品ID不能为空");
        }
    }

    /**
     * 检查SPU是否已存在（基于平台产品ID 或 PdcProductMappingId） - 复杂查询策略
     *
     * @param platformProductId 平台产品ID
     * @return SPU实体，不存在返回null
     */
    private TzProductSpu getExistingSpuByPlatformId(String platformProductId) {
        log.debug("检查SPU是否已存在，platformProductId: {}", platformProductId);
        // 1.1 优先通过主键查询
        TzProductSpu productSpu = tzProductSpuMapper.selectById(platformProductId);
        if (Objects.nonNull(productSpu)) {
            return productSpu;
        }
        // 1.2 再次尝试 通过 pdcProductMappingId 获取 spuId
        return CompletableFuture.supplyAsync(() -> {
            LambdaQueryWrapper<TzProductSpu> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(TzProductSpu::getPlatformCode, PlatformCodeEnum.PLATFORM_CODE_1688)
                .eq(TzProductSpu::getPdcPlatformProductId, platformProductId);
            return tzProductSpuMapper.selectOne(queryWrapper);
        }, virtualThreadExecutor)
            // 1.3 再次尝试 通过 pdcProductMappingId 获取 spuId
            .thenApplyAsync(result -> {
                if (result != null) {
                    return result;
                }
                LambdaQueryWrapper<TzProductSpu> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(TzProductSpu::getIsPdcSync, PdcProductMappingSyncStatusEnum.SYNCED)
                    .eq(TzProductSpu::getPdcProductMappingId, platformProductId);
                return tzProductSpuMapper.selectOne(queryWrapper);
            }, virtualThreadExecutor)
            // 异常处理
            .handleAsync((result, ex) -> {
                if (ex != null) {
                    log.error("查询SPU失败，platformProductId: {}", platformProductId, ex);
                    return null;
                }
                return result;
            }, virtualThreadExecutor).join();
    }

    /**
     * 从现有SPU构建产品DTO
     */
    private TzProductDTO buildProductDTOFromExisting(TzProductSpu spu, AlibabaProductDetailDTO detail) {
        List<TzProductSku> skuList = getSkuListBySpuId(spu.getId());

        // 确保单品有默认SKU
        if (CollectionUtil.isEmpty(skuList) && detail != null) {
            TzProductSku defaultSku = createDefaultSku(detail, spu);
            if (defaultSku != null) {
                skuList = List.of(defaultSku);
            }
        }

        return TzProductMapping.INSTANCE.toTzProductDTO(spu, skuList, detail);
    }

    /**
     * 在事务中创建新产品
     */
    private TzProductDTO createNewProductInTransaction(AlibabaProductDetailDTO productDetail) {
        // 创建SPU
        TzProductSpu spu = TzProductMapping.INSTANCE.toTzProductSpu(productDetail);

        try {
            tzProductSpuMapper.insert(spu);
            log.debug("SPU创建成功，spuId: {}, platformProductId: {}", spu.getId(), spu.getPdcPlatformProductId());
        } catch (Exception e) {
            log.error("SPU创建失败，platformProductId: {}", productDetail.getPlatformProductId(), e);
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.PRODUCT_DETAIL_GET_FAILED);
        }

        // 创建SKU
        List<TzProductSku> skuList = createSkuListInTransaction(productDetail, spu);

        return TzProductMapping.INSTANCE.toTzProductDTO(spu, skuList, productDetail);
    }

    /**
     * 在事务中更新现有产品
     */
    private TzProductDTO updateExistingProductInTransaction(TzProductSpu existingSpu, AlibabaProductDetailDTO productDetail) {
        // 更新SPU信息
        TzProductSpu updatedSpu = TzProductMapping.INSTANCE.toTzProductSpu(productDetail);
        updatedSpu.setId(existingSpu.getId());
        updatedSpu.setGmtCreated(existingSpu.getGmtCreated());

        try {
            tzProductSpuMapper.updateById(updatedSpu);
            log.debug("SPU更新成功，spuId: {}", updatedSpu.getId());
        } catch (Exception e) {
            log.error("SPU更新失败，spuId: {}", existingSpu.getId(), e);
        }

        // 重新创建SKU（简化处理，删除旧的创建新的）
        deleteExistingSkus(existingSpu.getId());
        List<TzProductSku> skuList = createSkuList(productDetail, updatedSpu);

        return TzProductMapping.INSTANCE.toTzProductDTO(updatedSpu, skuList, productDetail);
    }

    /**
     * 创建SKU列表
     */
    @Transactional(rollbackFor = Exception.class)
    private List<TzProductSku> createSkuList(AlibabaProductDetailDTO productDetail, TzProductSpu spu) {
        List<TzProductSku> skuList = new ArrayList<>();

        if (!productDetail.isSingleItem() && CollectionUtil.isNotEmpty(productDetail.getProductSkuList())) {
            // 多规格产品
            skuList = TzProductMapping.INSTANCE.toTzProductSkuList(
                productDetail.getProductSkuList(),
                spu.getId(),
                productDetail.getPlatformProductId(),
                spu.getMinOrderQuantity()
            );

            // 确保SKU图片
            ensureSkuImages(skuList, spu);

            if (CollectionUtil.isNotEmpty(skuList)) {
                tzProductSkuRepository.batchInsertSkus(skuList);
                log.debug("多规格SKU创建成功，数量: {}", skuList.size());
            }
        } else {
            // 单品
            TzProductSku defaultSku = createDefaultSku(productDetail, spu);
            if (defaultSku != null) {
                skuList.add(defaultSku);
            }
        }

        return skuList;
    }

    /**
     * 创建默认SKU（单品）
     */
    @Transactional(rollbackFor = Exception.class)
    private TzProductSku createDefaultSku(AlibabaProductDetailDTO productDetail, TzProductSpu spu) {
        TzProductSku defaultSku = TzProductMapping.INSTANCE.toSingleItemSku(productDetail, spu.getId());

        if (defaultSku != null) {
            // 确保SKU图片
            if (defaultSku.getImage() == null) {
                defaultSku.setImage(spu.getWhiteImage() != null ? spu.getWhiteImage() : spu.getMainImage());
            }

            try {
                tzProductSkuRepository.save(defaultSku);
                log.debug("单品默认SKU创建成功，skuId: {}", defaultSku.getId());
            } catch (Exception e) {
                log.error("单品默认SKU创建失败，spuId: {}", spu.getId(), e);
                return null;
            }
        }

        return defaultSku;
    }

    /**
     * 确保SKU图片不为空
     */
    private void ensureSkuImages(List<TzProductSku> skuList, TzProductSpu spu) {
        if (CollectionUtil.isNotEmpty(skuList)) {
            skuList.forEach(sku -> {
                if (sku.getImage() == null) {
                    sku.setImage(spu.getWhiteImage() != null ? spu.getWhiteImage() : spu.getMainImage());
                }
            });
        }
    }

    /**
     * 根据SPU ID获取SKU列表
     */
    private List<TzProductSku> getSkuListBySpuId(Long spuId) {
        try {
            LambdaQueryWrapper<TzProductSku> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(TzProductSku::getSpuId, spuId);
            return tzProductSkuRepository.list(queryWrapper);
        } catch (Exception e) {
            log.error("获取SKU列表失败，spuId: {}", spuId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 删除现有SKU
     */
    @Transactional(rollbackFor = Exception.class)
    private void deleteExistingSkus(Long spuId) {
        try {
            LambdaQueryWrapper<TzProductSku> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(TzProductSku::getSpuId, spuId);
            tzProductSkuRepository.remove(queryWrapper);
            log.debug("删除现有SKU成功，spuId: {}", spuId);
        } catch (Exception e) {
            log.error("删除现有SKU失败，spuId: {}", spuId, e);
        }
    }

    /**
     * 批量同步单个产品（用于批量处理）
     */
    private BatchSyncResult.SyncItem syncSingleProductForBatch(String productId, boolean forceUpdate) {
        try {
            TzProductDTO result = forceUpdate ? resyncProduct(productId, true) : syncProduct(productId);

            if (result != null) {
                return BatchSyncResult.SyncItem.success(productId, result);
            } else {
                return BatchSyncResult.SyncItem.failure(productId, "同步失败，产品详情未找到");
            }

        } catch (Exception e) {
            log.error("批量同步单个产品失败，productId: {}", productId, e);
            return BatchSyncResult.SyncItem.failure(productId, e.getMessage());
        }
    }

    /**
     * 统一异常处理
     */
    private void handleSyncException(Exception e, String productId) {
        if (e instanceof BusinessExceptionI18n) {
            log.error("同步产品业务异常，productId: {}", productId, e);
            throw (BusinessExceptionI18n) e;
        } else {
            log.error("同步产品系统异常，productId: {}", productId, e);
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.PRODUCT_DETAIL_GET_FAILED);
        }
    }
}
