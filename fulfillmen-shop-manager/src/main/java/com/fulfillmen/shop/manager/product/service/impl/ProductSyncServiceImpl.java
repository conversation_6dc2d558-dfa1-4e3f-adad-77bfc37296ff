/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.product.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fulfillmen.shop.common.enums.FulfillmenErrorCodeEnum;
import com.fulfillmen.shop.common.exception.BusinessExceptionI18n;
import com.fulfillmen.shop.common.util.MetaInfoHashUtils;
import com.fulfillmen.shop.dao.mapper.TzProductSpuMapper;
import com.fulfillmen.shop.domain.convert.TzProductMapping;
import com.fulfillmen.shop.domain.dto.TzProductDTO;
import com.fulfillmen.shop.domain.dto.product.alibaba.AlibabaProductDetailDTO;
import com.fulfillmen.shop.domain.entity.PdcProductMapping;
import com.fulfillmen.shop.domain.entity.TzProductSku;
import com.fulfillmen.shop.domain.entity.TzProductSpu;
import com.fulfillmen.shop.domain.entity.enums.PdcProductMappingSyncStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.PlatformCodeEnum;
import com.fulfillmen.shop.manager.core.repository.PdcProductMappingRepository;
import com.fulfillmen.shop.manager.core.repository.TzProductSkuRepository;
import com.fulfillmen.shop.manager.product.service.IProductSyncService;
import com.fulfillmen.shop.manager.service.dto.BatchSyncResult;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.StringUtils;

/**
 * 内部产品同步服务实现
 *
 * <pre>
 * 设计原则：
 * 1. 包级作用域 - 仅供ProductFacadeService内部使用
 * 2. 专注同步 - 不涉及价格策略等业务逻辑
 * 3. 高性能 - 使用虚拟线程和并发处理
 * 4. 容错性 - 完善的错误处理和降级机制
 *
 * 核心职责：
 * - 产品数据同步和获取
 * - 批量处理和并发优化
 * - 缓存策略和数据时效性管理
 * - 错误处理和异常恢复
 *
 * 实现特色：
 * - 复用原有同步逻辑，确保功能稳定性
 * - 简化接口设计，去除业务策略相关方法
 * - 优化批量处理性能，使用虚拟线程
 * - 统一错误处理，提供一致的异常信息
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/9/10
 * @since 2.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProductSyncServiceImpl implements IProductSyncService {

    private final PdcProductMappingRepository pdcProductMappingRepository;
    private final TzProductSkuRepository tzProductSkuRepository;
    private final TzProductSpuMapper tzProductSpuMapper;
    private final Executor virtualThreadExecutor;
    private final TransactionTemplate transactionTemplate;

    // ==================== 核心同步方法 ====================

    @Override
    public TzProductDTO syncProduct(String platformProductId) {
        validateProductId(platformProductId);

        log.debug("开始同步产品数据，platformProductId: {}", platformProductId);

        try {
            // 1. 检查SPU是否已存在（使用复杂的查询策略）
            TzProductSpu existingSpu = getExistingSpuByPlatformId(platformProductId);
            Long productId = Long.valueOf(platformProductId);

            // 异步获取产品详情以提升性能
            CompletableFuture<AlibabaProductDetailDTO> productDetailFuture = CompletableFuture
                .supplyAsync(() -> pdcProductMappingRepository.getProductDetailWithCache(productId, false),
                    virtualThreadExecutor);

            if (existingSpu != null) {
                log.debug("SPU已存在（基于平台产品ID），直接返回，spuId: {}", existingSpu.getId());
                // 获取现有的SKU列表
                List<TzProductSku> existingSkuList = getSkuListBySpuId(existingSpu.getId());
                // 如果不存在SKU，则创建一个
                if (CollectionUtil.isEmpty(existingSkuList)) {
                    log.warn("SKU列表为空，platformProductId: {}", platformProductId);
                    // 在事务中创建默认SKU
                    TzProductSku defaultSku = transactionTemplate.execute(status -> {
                        AlibabaProductDetailDTO detail = productDetailFuture.join();
                        return createDefaultSkuInTransaction(detail, existingSpu);
                    });
                    if (defaultSku != null) {
                        existingSkuList.add(defaultSku);
                        log.debug("单品默认SKU创建成功，skuId: {}", defaultSku.getId());
                    }
                }
                // 转换为DTO
                return TzProductMapping.INSTANCE.toTzProductDTO(existingSpu, existingSkuList, productDetailFuture.get());
            }

            // 2. 获取产品详情并创建新SPU
            AlibabaProductDetailDTO productDetail = productDetailFuture.get();
            if (productDetail == null) {
                log.warn("未找到产品详情，platformProductId: {}", platformProductId);
                return null;
            }

            // 使用编程式事务创建新产品
            return transactionTemplate.execute(status -> {
                try {
                    return createNewProductInTransaction(productDetail);
                } catch (Exception e) {
                    status.setRollbackOnly();
                    handleSyncException(e, platformProductId);
                    return null;
                }
            });

        } catch (Exception e) {
            handleSyncException(e, platformProductId);
            return null;
        }
    }

    @Override
    public TzProductDTO resyncProduct(String platformProductId, boolean forceUpdate) {
        validateProductId(platformProductId);

        log.debug("开始强制重新同步产品数据，platformProductId: {}, forceUpdate: {}",
            platformProductId, forceUpdate);

        try {
            Long productId = Long.valueOf(platformProductId);

            // 强制刷新获取最新产品详情
            AlibabaProductDetailDTO productDetail = pdcProductMappingRepository
                .getProductDetailWithCache(productId, true);

            if (productDetail == null) {
                log.warn("未找到产品详情，无法同步，platformProductId: {}", platformProductId);
                return null;
            }

            TzProductSpu existingSpu = getExistingSpuByPlatformId(platformProductId);

            if (existingSpu != null && forceUpdate) {
                // 强制更新模式：更新现有SPU
                return transactionTemplate.execute(status -> {
                    try {
                        return updateExistingProductInTransaction(existingSpu, productDetail);
                    } catch (Exception e) {
                        status.setRollbackOnly();
                        throw e;
                    }
                });
            } else if (existingSpu != null) {
                // 非强制更新：返回现有数据
                return buildProductDTOFromExisting(existingSpu, productDetail);
            } else {
                // 不存在：创建新产品
                return transactionTemplate.execute(status -> {
                    try {
                        return createNewProductInTransaction(productDetail);
                    } catch (Exception e) {
                        status.setRollbackOnly();
                        throw e;
                    }
                });
            }

        } catch (Exception e) {
            handleSyncException(e, platformProductId);
            return null;
        }
    }

    @Override
    public BatchSyncResult batchSyncProducts(List<String> platformProductIds, boolean forceUpdate) {
        // 参数校验和预处理
        if (platformProductIds == null) {
            return BatchSyncResult.createEmpty();
        }

        // 去重和过滤无效ID
        List<String> validIds = platformProductIds.stream()
            .filter(StringUtils::hasText)
            .distinct()
            .toList();

        if (validIds.isEmpty()) {
            return BatchSyncResult.createEmpty();
        }

        log.info("开始批量同步产品，总数: {}, 强制更新: {}", validIds.size(), forceUpdate);
        long startTime = System.currentTimeMillis();

        // 并发处理以提升性能
        List<CompletableFuture<BatchSyncResult.SyncItem>> futures = validIds.stream()
            .map(productId -> CompletableFuture.supplyAsync(
                () -> syncSingleProductForBatch(productId, forceUpdate),
                virtualThreadExecutor))
            .toList();

        // 等待所有任务完成并收集结果
        List<BatchSyncResult.SyncItem> results = futures.stream()
            .map(CompletableFuture::join)
            .collect(Collectors.toList());

        long executionTime = System.currentTimeMillis() - startTime;
        BatchSyncResult batchResult = BatchSyncResult.fromSyncItems(results, executionTime);

        log.info("批量同步产品完成，总数: {}, 成功: {}, 失败: {}, 跳过: {}, 耗时: {}ms",
            batchResult.getTotalCount(), batchResult.getSuccessCount(),
            batchResult.getFailureCount(), batchResult.getSkippedCount(), executionTime);

        return batchResult;
    }

    // ==================== 智能获取方法 ====================

    @Override
    public TzProductDTO getOrSyncProduct(String productId) {
        return getOrSyncProduct(productId, false);
    }

    @Override
    public TzProductDTO getOrSyncProduct(String productId, boolean forceSync) {
        validateProductId(productId);

        log.debug("智能获取或同步产品，productId: {}, forceSync: {}", productId, forceSync);

        try {
            // 先尝试从本地获取
            TzProductSpu existingSpu = getExistingSpuByPlatformId(productId);

            if (existingSpu != null) {
                // 检查是否需要刷新
                if (forceSync || needsRefresh(productId)) {
                    log.debug("产品数据需要刷新，执行重新同步，productId: {}", productId);
                    return resyncProduct(productId, true);
                } else {
                    log.debug("使用缓存的产品数据，productId: {}", productId);
                    return buildProductDTOFromExisting(existingSpu, getProductDetail(productId, false));
                }
            } else {
                // 本地不存在，执行同步
                log.debug("本地不存在产品数据，执行同步，productId: {}", productId);
                return syncProduct(productId);
            }

        } catch (Exception e) {
            log.error("智能获取或同步产品失败，productId: {}", productId, e);
            return null;
        }
    }

    // ==================== 产品详情方法 ====================

    @Override
    public AlibabaProductDetailDTO getProductDetail(String productId, boolean forceRefresh) {
        validateProductId(productId);

        try {
            Long id = Long.valueOf(productId);
            return pdcProductMappingRepository.getProductDetailWithCache(id, forceRefresh);
        } catch (Exception e) {
            log.error("获取产品详情失败，productId: {}", productId, e);
            return null;
        }
    }

    // ==================== 数据验证方法 ====================

    @Override
    public boolean isProductSynced(String platformProductId) {
        if (!StringUtils.hasText(platformProductId)) {
            return false;
        }

        try {
            return getExistingSpuByPlatformId(platformProductId) != null;
        } catch (Exception e) {
            log.error("检查产品同步状态失败，platformProductId: {}", platformProductId, e);
            return false;
        }
    }

    @Override
    public boolean needsRefresh(String platformProductId) {
        if (!StringUtils.hasText(platformProductId)) {
            return true;
        }

        try {
            TzProductSpu spu = getExistingSpuByPlatformId(platformProductId);
            if (spu == null) {
                return true;
            }

            // 检查数据是否超过2天
            LocalDateTime lastUpdate = spu.getGmtModified() != null ? spu.getGmtModified() : spu.getGmtCreated();
            Duration duration = Duration.between(lastUpdate, LocalDateTime.now());
            boolean needsRefresh = duration.toDays() >= 2;

            log.debug("产品数据时效检查，platformProductId: {}, 最后更新: {}, 需要刷新: {}",
                platformProductId, lastUpdate, needsRefresh);

            return needsRefresh;

        } catch (Exception e) {
            log.error("检查产品刷新需求失败，platformProductId: {}", platformProductId, e);
            return true;
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 参数校验
     */
    private void validateProductId(String productId) {
        if (!StringUtils.hasText(productId)) {
            throw new IllegalArgumentException("产品ID不能为空");
        }
    }

    /**
     * 检查SPU是否已存在（基于平台产品ID 或 PdcProductMappingId） - 复杂查询策略
     *
     * @param platformProductId 平台产品ID
     * @return SPU实体，不存在返回null
     */
    private TzProductSpu getExistingSpuByPlatformId(String platformProductId) {
        log.debug("检查SPU是否已存在，platformProductId: {}", platformProductId);
        // 1.1 优先通过主键查询
        TzProductSpu productSpu = tzProductSpuMapper.selectById(platformProductId);
        if (Objects.nonNull(productSpu)) {
            return productSpu;
        }
        // 1.2 再次尝试 通过 pdcProductMappingId 获取 spuId
        return CompletableFuture.supplyAsync(() -> {
            LambdaQueryWrapper<TzProductSpu> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(TzProductSpu::getPlatformCode, PlatformCodeEnum.PLATFORM_CODE_1688)
                .eq(TzProductSpu::getPdcPlatformProductId, platformProductId);
            return tzProductSpuMapper.selectOne(queryWrapper);
        }, virtualThreadExecutor)
            // 1.3 再次尝试 通过 pdcProductMappingId 获取 spuId
            .thenApplyAsync(result -> {
                if (result != null) {
                    return result;
                }
                LambdaQueryWrapper<TzProductSpu> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(TzProductSpu::getIsPdcSync, PdcProductMappingSyncStatusEnum.SYNCED)
                    .eq(TzProductSpu::getPdcProductMappingId, platformProductId);
                return tzProductSpuMapper.selectOne(queryWrapper);
            }, virtualThreadExecutor)
            // 异常处理
            .handleAsync((result, ex) -> {
                if (ex != null) {
                    log.error("查询SPU失败，platformProductId: {}", platformProductId, ex);
                    return null;
                }
                return result;
            }, virtualThreadExecutor).join();
    }

    /**
     * 从现有SPU构建产品DTO
     */
    private TzProductDTO buildProductDTOFromExisting(TzProductSpu spu, AlibabaProductDetailDTO detail) {
        List<TzProductSku> skuList = getSkuListBySpuId(spu.getId());

        // 确保单品有默认SKU
        if (CollectionUtil.isEmpty(skuList) && detail != null) {
            TzProductSku defaultSku = createDefaultSku(detail, spu);
            if (defaultSku != null) {
                skuList = List.of(defaultSku);
            }
        }

        return TzProductMapping.INSTANCE.toTzProductDTO(spu, skuList, detail);
    }

    /**
     * 在事务中创建新产品
     */
    private TzProductDTO createNewProductInTransaction(AlibabaProductDetailDTO productDetail) {
        // 创建SPU
        TzProductSpu spu = TzProductMapping.INSTANCE.toTzProductSpu(productDetail);

        try {
            tzProductSpuMapper.insert(spu);
            log.debug("SPU创建成功，spuId: {}, platformProductId: {}", spu.getId(), spu.getPdcPlatformProductId());
        } catch (Exception e) {
            log.error("SPU创建失败，platformProductId: {}", productDetail.getPlatformProductId(), e);
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.PRODUCT_DETAIL_GET_FAILED);
        }

        // 创建SKU
        List<TzProductSku> skuList = createSkuListInTransaction(productDetail, spu);

        return TzProductMapping.INSTANCE.toTzProductDTO(spu, skuList, productDetail);
    }

    /**
     * 在事务中更新现有产品
     */
    private TzProductDTO updateExistingProductInTransaction(TzProductSpu existingSpu, AlibabaProductDetailDTO productDetail) {
        // 更新SPU信息
        TzProductSpu updatedSpu = TzProductMapping.INSTANCE.toTzProductSpu(productDetail);
        updatedSpu.setId(existingSpu.getId());
        updatedSpu.setGmtCreated(existingSpu.getGmtCreated());

        try {
            tzProductSpuMapper.updateById(updatedSpu);
            log.debug("SPU更新成功，spuId: {}", updatedSpu.getId());
        } catch (Exception e) {
            log.error("SPU更新失败，spuId: {}", existingSpu.getId(), e);
        }

        // 重新创建SKU（简化处理，删除旧的创建新的）
        deleteExistingSkus(existingSpu.getId());
        List<TzProductSku> skuList = createSkuList(productDetail, updatedSpu);

        return TzProductMapping.INSTANCE.toTzProductDTO(updatedSpu, skuList, productDetail);
    }

    /**
     * 创建SKU列表
     */
    @Transactional(rollbackFor = Exception.class)
    private List<TzProductSku> createSkuList(AlibabaProductDetailDTO productDetail, TzProductSpu spu) {
        List<TzProductSku> skuList = new ArrayList<>();

        if (!productDetail.isSingleItem() && CollectionUtil.isNotEmpty(productDetail.getProductSkuList())) {
            // 多规格产品
            skuList = TzProductMapping.INSTANCE.toTzProductSkuList(
                productDetail.getProductSkuList(),
                spu.getId(),
                productDetail.getPlatformProductId(),
                spu.getMinOrderQuantity()
            );

            // 确保SKU图片
            ensureSkuImages(skuList, spu);

            if (CollectionUtil.isNotEmpty(skuList)) {
                tzProductSkuRepository.batchInsertSkus(skuList);
                log.debug("多规格SKU创建成功，数量: {}", skuList.size());
            }
        } else {
            // 单品
            TzProductSku defaultSku = createDefaultSku(productDetail, spu);
            if (defaultSku != null) {
                skuList.add(defaultSku);
            }
        }

        return skuList;
    }

    /**
     * 创建默认SKU（单品）
     */
    @Transactional(rollbackFor = Exception.class)
    private TzProductSku createDefaultSku(AlibabaProductDetailDTO productDetail, TzProductSpu spu) {
        TzProductSku defaultSku = TzProductMapping.INSTANCE.toSingleItemSku(productDetail, spu.getId());

        if (defaultSku != null) {
            // 确保SKU图片
            if (defaultSku.getImage() == null) {
                defaultSku.setImage(spu.getWhiteImage() != null ? spu.getWhiteImage() : spu.getMainImage());
            }

            try {
                tzProductSkuRepository.save(defaultSku);
                log.debug("单品默认SKU创建成功，skuId: {}", defaultSku.getId());
            } catch (Exception e) {
                log.error("单品默认SKU创建失败，spuId: {}", spu.getId(), e);
                return null;
            }
        }

        return defaultSku;
    }

    /**
     * 确保SKU图片不为空
     */
    private void ensureSkuImages(List<TzProductSku> skuList, TzProductSpu spu) {
        if (CollectionUtil.isNotEmpty(skuList)) {
            skuList.forEach(sku -> {
                if (sku.getImage() == null) {
                    sku.setImage(spu.getWhiteImage() != null ? spu.getWhiteImage() : spu.getMainImage());
                }
            });
        }
    }

    /**
     * 根据SPU ID获取SKU列表
     */
    private List<TzProductSku> getSkuListBySpuId(Long spuId) {
        try {
            LambdaQueryWrapper<TzProductSku> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(TzProductSku::getSpuId, spuId);
            return tzProductSkuRepository.list(queryWrapper);
        } catch (Exception e) {
            log.error("获取SKU列表失败，spuId: {}", spuId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 删除现有SKU
     */
    @Transactional(rollbackFor = Exception.class)
    private void deleteExistingSkus(Long spuId) {
        try {
            LambdaQueryWrapper<TzProductSku> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(TzProductSku::getSpuId, spuId);
            tzProductSkuRepository.remove(queryWrapper);
            log.debug("删除现有SKU成功，spuId: {}", spuId);
        } catch (Exception e) {
            log.error("删除现有SKU失败，spuId: {}", spuId, e);
        }
    }

    /**
     * 批量同步单个产品（用于批量处理）
     */
    private BatchSyncResult.SyncItem syncSingleProductForBatch(String productId, boolean forceUpdate) {
        try {
            TzProductDTO result = forceUpdate ? resyncProduct(productId, true) : syncProduct(productId);

            if (result != null) {
                return BatchSyncResult.SyncItem.success(productId, result);
            } else {
                return BatchSyncResult.SyncItem.failure(productId, "同步失败，产品详情未找到");
            }

        } catch (Exception e) {
            log.error("批量同步单个产品失败，productId: {}", productId, e);
            return BatchSyncResult.SyncItem.failure(productId, e.getMessage());
        }
    }

    /**
     * 统一异常处理
     */
    private void handleSyncException(Exception e, String productId) {
        if (e instanceof BusinessExceptionI18n) {
            log.error("同步产品业务异常，productId: {}", productId, e);
            throw (BusinessExceptionI18n) e;
        } else {
            log.error("同步产品系统异常，productId: {}", productId, e);
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.PRODUCT_DETAIL_GET_FAILED);
        }
    }

    /**
     * 在事务中创建默认SKU
     */
    private TzProductSku createDefaultSkuInTransaction(AlibabaProductDetailDTO detail, TzProductSpu existingSpu) {
        return createDefaultSku(detail, existingSpu);
    }

    /**
     * 在事务中创建SKU列表
     */
    private List<TzProductSku> createSkuListInTransaction(AlibabaProductDetailDTO productDetail, TzProductSpu spu) {
        return createSkuList(productDetail, spu);
    }

    // ==================== 扩展的SKU相关方法 ====================

    /**
     * 根据平台产品ID和平台SKU ID获取SKU
     *
     * @param platformProductId 平台产品ID
     * @param platformSkuId     平台SKU ID
     * @return SKU实体
     */
    public TzProductSku getSkuByPlatformIds(String platformProductId, String platformSkuId) {
        try {
            LambdaQueryWrapper<TzProductSku> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(TzProductSku::getPlatformProductId, platformProductId)
                .eq(TzProductSku::getPlatformSku, platformSkuId);
            return tzProductSkuRepository.getOne(queryWrapper);
        } catch (Exception e) {
            log.error("根据平台ID获取SKU失败，platformProductId: {}, platformSkuId: {}",
                platformProductId, platformSkuId, e);
            return null;
        }
    }

    /**
     * 获取单品的默认SKU
     *
     * @param spuId SPU ID
     * @return 默认SKU，不存在返回null
     */
    public TzProductSku getSingleItemDefaultSku(Long spuId) {
        try {
            LambdaQueryWrapper<TzProductSku> queryWrapper = new LambdaQueryWrapper<>();
            // 单品只有一个SKU
            queryWrapper.eq(TzProductSku::getSpuId, spuId).last("LIMIT 1");
            return tzProductSkuRepository.getOne(queryWrapper);
        } catch (Exception e) {
            log.error("获取单品默认SKU失败，spuId: {}", spuId, e);
            return null;
        }
    }

    /**
     * 检查产品是否为单品
     *
     * @param platformProductId 平台产品ID
     * @return true-单品，false-多规格商品
     */
    public boolean isSingleItem(String platformProductId) {
        try {
            Long productId = Long.valueOf(platformProductId);
            AlibabaProductDetailDTO productDetail = pdcProductMappingRepository
                .getProductDetailWithCache(productId, false);
            return productDetail != null && productDetail.isSingleItem();
        } catch (Exception e) {
            log.warn("检查产品是否为单品失败，platformProductId: {}", platformProductId, e);
            return false;
        }
    }

    /**
     * 获取单品价格
     *
     * @param platformProductId 平台产品ID
     * @return 单品价格
     */
    public BigDecimal getSingleItemPrice(String platformProductId) {
        try {
            Long productId = Long.valueOf(platformProductId);
            AlibabaProductDetailDTO productDetail = pdcProductMappingRepository
                .getProductDetailWithCache(productId, false);
            if (productDetail == null) {
                log.warn("获取单品价格失败，未找到产品详情，platformProductId: {}", platformProductId);
                return null;
            }

            // 使用 Mapstruct 中的价格提取逻辑
            return TzProductMapping.INSTANCE.extractDropShippingPriceFromProductDetail(productDetail);
        } catch (Exception e) {
            log.error("获取单品价格异常，platformProductId: {}", platformProductId, e);
            return null;
        }
    }

    /**
     * 强制为单品创建默认SKU
     *
     * @param spuId             SPU ID
     * @param platformProductId 平台产品ID
     * @return 创建的默认SKU
     */
    @Transactional(rollbackFor = Exception.class)
    public TzProductSku forceCreateDefaultSkuForSingleItem(Long spuId, String platformProductId) {
        log.warn("强制为单品创建默认SKU，spuId: {}, platformProductId: {}", spuId, platformProductId);

        try {
            // 1. 检查是否已存在默认SKU
            TzProductSku existingSku = getSingleItemDefaultSku(spuId);
            if (existingSku != null) {
                log.debug("单品默认SKU已存在，直接返回，skuId: {}", existingSku.getId());
                return existingSku;
            }

            // 2. 获取SPU信息
            TzProductSpu spu = tzProductSpuMapper.selectById(spuId);
            if (spu == null) {
                log.error("SPU不存在，无法创建默认SKU，spuId: {}", spuId);
                return null;
            }

            // 3. 从PdcProductMapping获取产品详情
            Long productId = Long.valueOf(platformProductId);
            AlibabaProductDetailDTO productDetail = pdcProductMappingRepository
                .getProductDetailWithCache(productId, false);
            if (productDetail == null) {
                log.warn("未找到产品详情，使用基础信息创建默认SKU，platformProductId: {}", platformProductId);
                // 使用基础信息创建默认SKU
                return createEmergencyDefaultSku(spuId, platformProductId, spu);
            }

            // 4. 使用 Mapstruct 创建默认SKU
            TzProductSku defaultSku = TzProductMapping.INSTANCE.toSingleItemSku(productDetail, spuId);
            if (defaultSku != null) {
                tzProductSkuRepository.save(defaultSku);
                log.info("强制创建单品默认SKU成功，spuId: {}, skuId: {}", spuId, defaultSku.getId());
                return defaultSku;
            } else {
                log.error("创建单品默认SKU失败，spuId: {}, platformProductId: {}", spuId, platformProductId);
                return null;
            }
        } catch (Exception e) {
            log.error("强制创建单品默认SKU异常，spuId: {}, platformProductId: {}", spuId, platformProductId, e);
            return null;
        }
    }

    // ==================== 智能SKU同步逻辑 ====================

    /**
     * 智能更新SPU的SKU数据（增量同步，保护现有数据）
     *
     * <pre>
     * 优化策略：
     * 1. 通过 platformSku 字段匹配现有SKU和新SKU
     * 2. 更新现有SKU的数据（价格、库存、规格等）
     * 3. 新增缺少的SKU
     * 4. 对于不存在的SKU：设置库存为0或执行逻辑删除
     * 5. 保留现有SKU ID，避免订单数据关联问题
     * </pre>
     *
     * @param spuId         SPU ID
     * @param productDetail 最新产品详情
     * @return 更新后的SKU列表
     */
    @Transactional(rollbackFor = Exception.class)
    public List<TzProductSku> intelligentUpdateSkusForSpu(Long spuId, AlibabaProductDetailDTO productDetail) {
        log.debug("开始智能更新SKU数据，spuId: {}", spuId);

        try {
            // 1. 获取现有SKU列表
            List<TzProductSku> existingSkuList = getSkuListBySpuId(spuId);
            log.debug("现有SKU数量: {}", existingSkuList.size());

            // 2. 构建新的SKU数据
            List<TzProductSku> newSkuDataList = buildNewSkuDataList(spuId, productDetail);
            log.debug("新SKU数据量: {}", newSkuDataList.size());

            // 3. 执行增量更新
            List<TzProductSku> updatedSkuList = performIncrementalSkuUpdate(existingSkuList, newSkuDataList);

            log.info("SKU智能更新完成，spuId: {}, 最终SKU数量: {}", spuId, updatedSkuList.size());
            return updatedSkuList;

        } catch (Exception e) {
            log.error("智能更新SKU失败，spuId: {}", spuId, e);
            // 降级到原有逻辑
            log.warn("降级使用原有SKU更新逻辑，spuId: {}", spuId);
            deleteExistingSkus(spuId);
            return createSkuList(productDetail, tzProductSpuMapper.selectById(spuId));
        }
    }

    /**
     * 构建新的SKU数据列表
     */
    private List<TzProductSku> buildNewSkuDataList(Long spuId, AlibabaProductDetailDTO productDetail) {
        List<TzProductSku> newSkuDataList = new ArrayList<>();

        if (!productDetail.isSingleItem() && CollectionUtil.isNotEmpty(productDetail.getProductSkuList())) {
            // 多规格产品
            newSkuDataList = TzProductMapping.INSTANCE.toTzProductSkuList(
                productDetail.getProductSkuList(),
                spuId,
                productDetail.getPlatformProductId(),
                productDetail.getMinOrderQuantity()
            );
        } else {
            // 单品
            TzProductSku defaultSku = TzProductMapping.INSTANCE.toSingleItemSku(productDetail, spuId);
            if (defaultSku != null) {
                newSkuDataList.add(defaultSku);
            }
        }

        // 确保图片URL不为空
        TzProductSpu spu = tzProductSpuMapper.selectById(spuId);
        if (spu != null && CollectionUtil.isNotEmpty(newSkuDataList)) {
            newSkuDataList.forEach(sku -> {
                if (sku.getImage() == null) {
                    if (spu.getWhiteImage() != null) {
                        sku.setImage(spu.getWhiteImage());
                    } else if (spu.getMainImage() != null) {
                        sku.setImage(spu.getMainImage());
                    }
                }
            });
        }

        return newSkuDataList;
    }

    /**
     * 执行增量SKU更新
     */
    private List<TzProductSku> performIncrementalSkuUpdate(List<TzProductSku> existingSkuList, List<TzProductSku> newSkuDataList) {
        log.debug("执行增量SKU更新，现有SKU: {}, 新SKU数据: {}", existingSkuList.size(), newSkuDataList.size());

        // 使用Map来快速查找现有SKU
        Map<String, TzProductSku> existingSkuMap = existingSkuList.stream()
            .collect(Collectors.toMap(TzProductSku::getPlatformSku, sku -> sku, (existing, replacement) -> existing));

        // 使用Map来快速查找新SKU
        Map<String, TzProductSku> newSkuMap = newSkuDataList.stream()
            .collect(Collectors.toMap(TzProductSku::getPlatformSku, sku -> sku, (existing, replacement) -> existing));

        List<TzProductSku> updatedSkuList = new ArrayList<>();
        List<TzProductSku> skusToCreate = new ArrayList<>();

        // 处理新SKU数据
        for (TzProductSku newSkuData : newSkuDataList) {
            String platformSku = newSkuData.getPlatformSku();
            TzProductSku existingSku = existingSkuMap.get(platformSku);

            if (existingSku != null) {
                // 更新现有SKU
                TzProductSku updatedSku = updateExistingSku(existingSku, newSkuData);
                updatedSkuList.add(updatedSku);
                log.debug("更新现有SKU: {}", platformSku);
            } else {
                // 需要创建新SKU
                skusToCreate.add(newSkuData);
                log.debug("标记创建新SKU: {}", platformSku);
            }
        }

        // 处理不存在的SKU（在源系统中已删除的SKU）
        for (TzProductSku existingSku : existingSkuList) {
            String platformSku = existingSku.getPlatformSku();
            if (!newSkuMap.containsKey(platformSku)) {
                // SKU在源系统中不存在，设置库存为0（逻辑删除策略）
                TzProductSku obsoleteSku = handleObsoleteSku(existingSku);
                updatedSkuList.add(obsoleteSku);
                log.debug("处理已删除SKU: {}，设置库存为0", platformSku);
            }
        }

        // 批量创建新SKU
        if (CollectionUtil.isNotEmpty(skusToCreate)) {
            try {
                tzProductSkuRepository.batchInsertSkus(skusToCreate);
                updatedSkuList.addAll(skusToCreate);
                log.info("批量创建新SKU成功，数量: {}", skusToCreate.size());
            } catch (Exception e) {
                log.error("批量创建SKU失败", e);
                // 尝试逐个创建
                for (TzProductSku sku : skusToCreate) {
                    try {
                        tzProductSkuRepository.save(sku);
                        updatedSkuList.add(sku);
                        log.debug("单个创建SKU成功: {}", sku.getPlatformSku());
                    } catch (Exception ex) {
                        log.error("创建SKU失败: {}", sku.getPlatformSku(), ex);
                    }
                }
            }
        }

        return updatedSkuList;
    }

    /**
     * 更新现有SKU数据
     */
    private TzProductSku updateExistingSku(TzProductSku existingSku, TzProductSku newSkuData) {
        log.debug("更新现有SKU数据: {}", existingSku.getPlatformSku());

        // 只更新需要同步的字段，保留现有的ID和其他关键信息
        existingSku.setSku(newSkuData.getSku());
        existingSku.setBarcode(newSkuData.getBarcode());
        existingSku.setPrice(newSkuData.getPrice());
        existingSku.setDropShippingPrice(newSkuData.getDropShippingPrice());
        existingSku.setQuantity(newSkuData.getQuantity());
        existingSku.setSpecs(newSkuData.getSpecs());

        // 更新图片，但保留现有图片如果新图片为空
        if (newSkuData.getImage() != null) {
            existingSku.setImage(newSkuData.getImage());
        }

        // 执行数据库更新
        try {
            boolean updateResult = tzProductSkuRepository.updateById(existingSku);
            if (updateResult) {
                log.debug("SKU更新成功: {}", existingSku.getPlatformSku());
            } else {
                log.warn("SKU更新失败，可能是乐观锁冲突: {}", existingSku.getPlatformSku());
            }
        } catch (Exception e) {
            log.error("SKU数据库更新异常: {}", existingSku.getPlatformSku(), e);
        }

        return existingSku;
    }

    /**
     * 处理已删除的SKU（在源系统中不存在的SKU）
     * 策略：设置库存为0，保留SKU记录以保护现有订单数据
     */
    private TzProductSku handleObsoleteSku(TzProductSku existingSku) {
        log.debug("处理已删除SKU: {}", existingSku.getPlatformSku());

        // 设置库存为0，表示该SKU已不可用
        existingSku.setQuantity(0);

        // 可以选择设置一个特殊标记，表示该SKU已被逻辑删除
        // 这里我们保持简单，只设置库存为0

        try {
            boolean updateResult = tzProductSkuRepository.updateById(existingSku);
            if (updateResult) {
                log.debug("已删除SKU处理成功，设置库存为0: {}", existingSku.getPlatformSku());
            } else {
                log.warn("已删除SKU处理失败: {}", existingSku.getPlatformSku());
            }
        } catch (Exception e) {
            log.error("处理已删除SKU异常: {}", existingSku.getPlatformSku(), e);
        }

        return existingSku;
    }

    /**
     * 创建应急默认SKU（当产品详情不可用时）
     */
    private TzProductSku createEmergencyDefaultSku(Long spuId, String platformProductId, TzProductSpu spu) {
        try {
            long skuId = TzProductMapping.INSTANCE.generateId();
            TzProductSku emergencySku = TzProductSku.builder()
                .id(skuId)
                .spuId(spuId)
                .platformCode(PlatformCodeEnum.PLATFORM_CODE_1688)
                .platformProductId(platformProductId)
                .platformSku(platformProductId)
                .sku("SKU_" + skuId)
                .barcode(String.valueOf(skuId))
                .image(spu.getMainImage())
                .price(BigDecimal.ZERO)
                .dropShippingPrice(BigDecimal.ZERO)
                // 空规格
                .specs(List.of())
                .quantity(0)
                .build();

            tzProductSkuRepository.save(emergencySku);
            log.info("创建应急默认SKU成功，spuId: {}, skuId: {}", spuId, emergencySku.getId());
            return emergencySku;
        } catch (Exception e) {
            log.error("创建应急默认SKU失败，spuId: {}, platformProductId: {}", spuId, platformProductId, e);
            return null;
        }
    }

    // ==================== 数据时效性检查方法 ====================

    /**
     * 检查是否需要重新同步数据
     *
     * <pre>
     * 🔥 优化后的智能检测逻辑：
     * 1. 优先通过 metaInfoHash 检测数据变更（MD5签名对比）
     * 2. 兜底使用时间间隔检测（超过3天）
     * 3. 避免不必要的API调用，提升性能
     * </pre>
     *
     * @param platformProductId 平台产品ID
     * @return 是否需要重新同步
     */
    private boolean checkIfNeedResync(String platformProductId) {
        try {
            // 2. 🔥 智能检测：通过metaInfoHash检测数据变更
            if (hasDataChangedByHash(platformProductId)) {
                log.debug("检测到数据变更（基于metaInfoHash），需要重新同步: platformProductId={}", platformProductId);
                return true;
            }

            // 3. 兜底机制：检查时间间隔
            if (hasDataExpiredByTime(platformProductId)) {
                log.debug("数据已过期（基于时间检测），需要重新同步: platformProductId={}", platformProductId);
                return true;
            }

            log.debug("数据无需重新同步: platformProductId={}", platformProductId);
            return false;

        } catch (Exception e) {
            log.warn("检查数据同步状态失败，默认不同步: platformProductId={}", platformProductId, e);
            return false;
        }
    }

    /**
     * 🔥 通过metaInfoHash检测数据是否变更
     *
     * @param platformProductId 平台产品ID
     * @return true-数据已变更，false-数据未变更
     */
    private boolean hasDataChangedByHash(String platformProductId) {
        try {
            // 获取最新的商品详情数据
            // 强制刷新获取最新数据
            AlibabaProductDetailDTO latestProductDetail = pdcProductMappingRepository
                .getProductDetailWithCache(Long.valueOf(platformProductId), true);

            if (latestProductDetail == null) {
                log.debug("无法获取最新商品详情，跳过hash检测: {}", platformProductId);
                return false;
            }

            // 构建最新数据的metaInfo并计算MD5
            String latestHash = MetaInfoHashUtils.calculateMetaInfoHash(latestProductDetail);

            // 这里需要获取现有的metaInfoHash进行比较
            // 由于当前架构限制，暂时返回false，后续完善
            // TODO: 需要通过Repository获取现有的PdcProductMapping.metaInfoHash
            log.debug("计算得到最新hash: platformProductId={}, hash={}", platformProductId, latestHash);
            // 暂时返回false，后续完善
            return false;

        } catch (Exception e) {
            log.warn("metaInfoHash检测失败: platformProductId={}", platformProductId, e);
            return false;
        }
    }

    /**
     * 检查数据是否基于时间过期
     *
     * @param platformProductId 平台产品ID
     * @return true-已过期，false-未过期
     */
    private boolean hasDataExpiredByTime(String platformProductId) {
        try {
            // 通过Repository获取PdcProductMapping实体
            PdcProductMapping pdcProductMapping = pdcProductMappingRepository.getByPlatformProductId(platformProductId);

            if (pdcProductMapping == null) {
                log.debug("PdcProductMapping不存在，认为需要同步: platformProductId={}", platformProductId);
                return true; // 数据不存在，需要同步
            }

            LocalDateTime lastModified = pdcProductMapping.getGmtModified();
            if (lastModified == null) {
                log.debug("PdcProductMapping修改时间为空，认为需要同步: platformProductId={}", platformProductId);
                return true; // 修改时间为空，需要同步
            }

            // 检查数据是否超过3天未更新
            LocalDateTime now = LocalDateTime.now();
            Duration timeSinceUpdate = Duration.between(lastModified, now);
            boolean isExpired = timeSinceUpdate.toDays() >= 3;

            if (isExpired) {
                log.debug("PdcProductMapping数据已过期，需要重新同步: platformProductId={}, 最后更新时间={}, 距今{}天",
                    platformProductId, lastModified, timeSinceUpdate.toDays());
            } else {
                log.debug("PdcProductMapping数据未过期: platformProductId={}, 最后更新时间={}, 距今{}天",
                    platformProductId, lastModified, timeSinceUpdate.toDays());
            }

            return isExpired;

        } catch (Exception e) {
            log.warn("时间过期检测失败: platformProductId={}", platformProductId, e);
            // 异常情况下不强制同步
            return false;
        }
    }
}
