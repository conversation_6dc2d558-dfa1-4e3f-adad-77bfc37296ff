/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.product.service.impl;

import com.fulfillmen.shop.domain.dto.PageDTO;
import com.fulfillmen.shop.domain.dto.ProductInfoDTO;
import com.fulfillmen.shop.domain.dto.ProductSearchRequestDTO;
import com.fulfillmen.shop.domain.dto.TzProductDTO;
import com.fulfillmen.shop.domain.dto.TzProductSkuDTO;
import com.fulfillmen.shop.domain.dto.product.alibaba.AlibabaProductDetailDTO;
import com.fulfillmen.shop.domain.req.AggregateSearchReq;
import com.fulfillmen.shop.manager.core.repository.PdcProductMappingRepository;
import com.fulfillmen.shop.manager.product.service.IProductFacadeService;
import com.fulfillmen.shop.manager.product.service.IProductStrategyService;
import com.fulfillmen.shop.manager.product.service.IProductSyncService;
import com.fulfillmen.shop.manager.service.dto.BatchSyncResult;
import com.fulfillmen.starter.core.exception.BusinessException;
import com.fulfillmen.support.alibaba.enums.LanguageEnum;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * 产品门面服务实现 V2.1 - 架构统一版
 *
 * <pre>
 * 架构优化（V2.1）：
 * 1. 包结构统一：门面服务与内部服务均位于service包，结构清晰一致
 * 2. 依赖关系明确：门面服务聚合所有内部包级可见服务
 * 3. 作用域控制：内部服务设为包级可见，仅供门面服务使用
 * 4. 职责分离：门面层专注外部接口，内部服务专注具体职责
 *
 * 服务整合：
 * - IProductSyncService：内部产品同步服务（包级可见）
 * - IProductStrategyService：内部产品策略服务（包级可见）
 * - PdcProductMappingRepository：产品数据仓储服务
 * - IProductFacadeService：外部统一门面接口（公开）
 *
 * 核心职责：
 * 1. 提供统一的产品获取入口，自动处理缓存、同步、价格策略
 * 2. 集成内部服务能力，对外暴露简洁的业务接口
 * 3. 统一错误处理和降级机制
 * 4. 性能优化：并发处理、智能缓存、批量操作
 *
 * 设计原则：
 * - 简单优于复杂：内部处理复杂逻辑，对外提供简单接口
 * - 自动化处理：无需调用方关心缓存、同步等底层逻辑
 * - 性能优先：采用并发和缓存提升性能
 * - 容错设计：单点失败不影响整体功能
 * - 架构统一：所有服务层组件位于同一包下，作用域清晰
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/9/8
 * @since 2.0.0
 * @version 2.1.0 - 包结构统一，架构优化
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProductFacadeServiceImpl implements IProductFacadeService {

    private final PdcProductMappingRepository pdcProductMappingRepository;
    private final IProductSyncService productSyncService;
    private final IProductStrategyService productStrategyService;
    private final Executor virtualThreadExecutor;

    // ==================== 单个产品获取 ====================

    @Override
    public TzProductDTO getProduct(String productId) {
        return getProduct(productId, true);
    }

    @Override
    public TzProductDTO getProduct(String productId, boolean applyPricing) {
        // 使用智能同步方式获取产品，保持向后兼容
        return getOrSyncProduct(productId, false, applyPricing);
    }

    @Override
    public TzProductDTO getOrSyncProduct(String productId) {
        return getOrSyncProduct(productId, false, true);
    }

    @Override
    public TzProductDTO getOrSyncProduct(String productId, boolean forceSync) {
        return getOrSyncProduct(productId, forceSync, true);
    }

    @Override
    public TzProductDTO getOrSyncProduct(String productId, boolean forceSync, boolean applyPricing) {
        // 参数校验
        if (!StringUtils.hasText(productId)) {
            throw new IllegalArgumentException("产品ID不能为空");
        }

        log.debug("获取或同步产品，ID: {}, 强制同步: {}, 应用价格策略: {}", productId, forceSync, applyPricing);

        try {
            TzProductDTO product;

            if (forceSync) {
                // 强制同步模式：使用 resyncProduct 强制重新同步
                log.debug("执行强制同步，产品ID: {}", productId);
                product = productSyncService.resyncProduct(productId, true);
            } else {
                // 智能同步模式：使用 getOrSyncProduct 智能判断
                log.debug("执行智能同步，产品ID: {}", productId);
                product = productSyncService.getOrSyncProduct(productId);
            }

            if (product == null) {
                log.debug("产品未找到，ID: {}", productId);
                return null;
            }

            // 应用价格策略（如果需要）
            if (applyPricing) {
                product = applyPricingWithFallback(product, productId);
            }

            log.debug("成功获取或同步产品，ID: {}, 名称: {}, 强制同步: {}",
                productId, product.getTitle(), forceSync);
            return product;

        } catch (Exception e) {
            log.error("获取或同步产品失败，ID: {}, 强制同步: {}", productId, forceSync, e);
            throw new BusinessException("获取或同步产品失败: " + e.getMessage());
        }
    }

    // ==================== 批量产品获取 ====================

    @Override
    public List<TzProductDTO> getProducts(List<String> productIds) {
        return getProducts(productIds, true);
    }

    @Override
    public List<TzProductDTO> getProducts(List<String> productIds, boolean applyPricing) {
        // 参数校验
        if (productIds == null) {
            throw new IllegalArgumentException("产品ID列表不能为null");
        }

        if (productIds.isEmpty()) {
            return Collections.emptyList();
        }

        log.debug("批量获取产品，数量: {}, 应用价格策略: {}", productIds.size(), applyPricing);

        try {
            // 使用并发获取提升性能
            List<CompletableFuture<TzProductDTO>> futures = productIds.stream()
                .map(productId -> CompletableFuture.supplyAsync(
                    () -> getSingleProductSafely(productId, applyPricing),
                    virtualThreadExecutor))
                .toList();

            // 等待所有任务完成并收集结果
            List<TzProductDTO> results = futures.stream()
                .map(CompletableFuture::join)
                .collect(Collectors.toList());

            long successCount = results.stream().mapToLong(p -> p != null ? 1 : 0).sum();
            log.debug("批量获取产品完成，总数: {}, 成功: {}", productIds.size(), successCount);

            return results;

        } catch (Exception e) {
            log.error("批量获取产品失败", e);
            throw new BusinessException("批量获取产品失败: " + e.getMessage());
        }
    }

    // ==================== 产品搜索和推荐 ====================

    @Override
    public List<ProductInfoDTO> searchProducts(String keyword) {
        // 参数校验
        if (!StringUtils.hasText(keyword)) {
            throw new IllegalArgumentException("搜索关键词不能为空");
        }

        log.debug("搜索产品（轻量级模式），关键词: {}", keyword);

        try {
            // 构建搜索请求
            ProductSearchRequestDTO request = ProductSearchRequestDTO.builder()
                .keyword(keyword)
                .page(1)
                .pageSize(20)
                .build();

            // 执行搜索（带缓存和同步）
            // 对象引用污染问题已通过深拷贝机制解决，可以恢复正常缓存
            PageDTO<ProductInfoDTO> searchResult = pdcProductMappingRepository.searchProductInfoListSyncWithCache(request, false);

            if (searchResult == null || searchResult.getRecords() == null) {
                log.debug("搜索结果为空，关键词: {}", keyword);
                return Collections.emptyList();
            }

            // 轻量级价格策略应用 - 仅对现有数据应用价格策略，不进行额外同步
            List<ProductInfoDTO> resultList = applyLightweightPricing(searchResult.getRecords());

            log.debug("搜索产品完成（轻量级模式），关键词: {}, 结果数量: {}", keyword, resultList.size());
            return resultList;

        } catch (Exception e) {
            log.error("搜索产品失败（轻量级模式），关键词: {}", keyword, e);
            throw new BusinessException("搜索产品失败: " + e.getMessage());
        }
    }

    @Override
    public List<TzProductDTO> searchProductsDetailed(String keyword) {
        // 参数校验
        if (!StringUtils.hasText(keyword)) {
            throw new IllegalArgumentException("搜索关键词不能为空");
        }

        log.debug("搜索产品（详细模式），关键词: {}", keyword);

        try {
            // 构建搜索请求
            ProductSearchRequestDTO request = ProductSearchRequestDTO.builder()
                .keyword(keyword)
                .page(1)
                .pageSize(20)
                .build();

            // 执行搜索（带缓存和同步）
            PageDTO<ProductInfoDTO> searchResult = pdcProductMappingRepository
                .searchProductInfoListSyncWithCache(request, false);

            if (searchResult == null || searchResult.getRecords() == null) {
                log.debug("搜索结果为空，关键词: {}", keyword);
                return Collections.emptyList();
            }

            // 转换并应用价格策略（详细模式）
            List<TzProductDTO> products = convertAndApplyStrategy(searchResult.getRecords(), true);

            log.debug("搜索产品完成（详细模式），关键词: {}, 结果数量: {}", keyword, products.size());
            return products;

        } catch (Exception e) {
            log.error("搜索产品失败（详细模式），关键词: {}", keyword, e);
            throw new BusinessException("搜索产品失败: " + e.getMessage());
        }
    }

    @Override
    public List<ProductInfoDTO> getTrendingProducts() {
        log.debug("获取推荐产品（轻量级模式）");

        try {
            // 获取推荐产品（使用优化后的缓存+同步方法）
            PageDTO<ProductInfoDTO> recommendResult = pdcProductMappingRepository.recommendProductsWithCache(LanguageEnum.EN, 1, 20, false);

            List<ProductInfoDTO> productInfoDTOList = Objects.isNull(recommendResult) ? null : recommendResult.getRecords();
            if (CollectionUtils.isEmpty(productInfoDTOList)) {
                log.debug("推荐产品数据为空");
                return Collections.emptyList();
            }

            // 轻量级价格策略应用 - 仅对现有数据应用价格策略，不进行额外同步
            List<ProductInfoDTO> productInfoDTOListPrice = applyLightweightPricing(productInfoDTOList);

            log.debug("获取推荐产品完成，数量: {}", productInfoDTOListPrice.size());
            return productInfoDTOListPrice;

        } catch (Exception e) {
            log.error("获取推荐产品失败", e);
            throw new BusinessException("获取推荐产品失败: " + e.getMessage());
        }
    }

    @Override
    public List<TzProductDTO> getTrendingProductsDetailed() {
        log.debug("获取推荐产品（详细模式）");

        try {
            // 获取推荐产品
            PageDTO<ProductInfoDTO> recommendResult = pdcProductMappingRepository.recommendProductsWithCache(LanguageEnum.EN, 1, 20, false);

            List<ProductInfoDTO> productInfoDTOList = Objects.isNull(recommendResult) ? null : recommendResult.getRecords();
            if (CollectionUtils.isEmpty(productInfoDTOList)) {
                log.debug("推荐产品数据为空");
                return Collections.emptyList();
            }

            // 转换并应用价格策略（详细模式）
            List<TzProductDTO> products = convertAndApplyStrategy(recommendResult.getRecords(), true);

            log.debug("获取推荐产品完成（详细模式），数量: {}", products.size());
            return products;

        } catch (Exception e) {
            log.error("获取推荐产品失败（详细模式）", e);
            throw new BusinessException("获取推荐产品失败: " + e.getMessage());
        }
    }

    // ==================== 产品同步和详情 ====================

    @Override
    public TzProductDTO syncProduct(String platformProductId) {
        // 参数校验
        if (!StringUtils.hasText(platformProductId)) {
            throw new IllegalArgumentException("平台产品ID不能为空");
        }

        log.debug("同步产品，平台ID: {}", platformProductId);

        try {
            // 执行同步
            TzProductDTO product = productSyncService.syncProduct(platformProductId);

            if (product == null) {
                log.debug("同步产品失败，平台ID: {}", platformProductId);
                return null;
            }

            // 自动应用价格策略
            product = applyPricingWithFallback(product, platformProductId);

            log.debug("同步产品成功，平台ID: {}, 产品名称: {}", platformProductId, product.getTitle());
            return product;

        } catch (Exception e) {
            log.error("同步产品失败，平台ID: {}", platformProductId, e);
            throw new BusinessException("同步产品失败: " + e.getMessage());
        }
    }

    @Override
    public BatchSyncResult batchSyncProducts(List<String> platformProductIds) {
        // 参数校验
        if (platformProductIds == null) {
            throw new IllegalArgumentException("平台产品ID列表不能为null");
        }

        if (platformProductIds.isEmpty()) {
            log.debug("批量同步产品，输入列表为空，返回空结果");
            return BatchSyncResult.builder()
                .totalCount(0)
                .successCount(0)
                .failureCount(0)
                .skippedCount(0)
                .executionTimeMs(0L)
                .build();
        }

        log.debug("批量同步产品，数量: {}", platformProductIds.size());

        try {
            // 直接委托给底层服务的批量同步方法
            // 该方法已经包含了完整的并发处理、错误处理、统计等功能
            BatchSyncResult result = productSyncService.batchSyncProducts(platformProductIds, false);

            if (result != null && !result.getSuccessProducts().isEmpty()) {
                // 对成功同步的产品应用价格策略
                List<TzProductDTO> processedProducts = result.getSuccessProducts().stream()
                    .map(product -> applyPricingWithFallback(product, product.getPdcPlatformProductId() != null
                        ? product.getPdcPlatformProductId()
                        : "unknown"))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

                // 更新结果中的产品列表
                result.setSuccessProducts(processedProducts);
            }

            log.info("批量同步产品完成，总数: {}, 成功: {}, 失败: {}, 跳过: {}, 耗时: {}ms",
                result.getTotalCount(), result.getSuccessCount(), result.getFailureCount(),
                result.getSkippedCount(), result.getExecutionTimeMs());

            return result;

        } catch (Exception e) {
            log.error("批量同步产品失败，输入数量: {}", platformProductIds.size(), e);
            throw new BusinessException("批量同步产品失败: " + e.getMessage());
        }
    }

    @Override
    public AlibabaProductDetailDTO getProductDetail(String productId) {
        // 参数校验
        if (!StringUtils.hasText(productId)) {
            throw new IllegalArgumentException("产品ID不能为空");
        }

        log.debug("获取产品详情，ID: {}", productId);

        try {
            // 获取产品详情（带缓存）
            AlibabaProductDetailDTO detail = productSyncService.getProductDetail(productId, false);

            if (detail == null) {
                log.debug("产品详情未找到，ID: {}", productId);
                return null;
            }

            log.debug("获取产品详情成功，ID: {}", productId);
            return detail;

        } catch (Exception e) {
            log.error("获取产品详情失败，ID: {}", productId, e);
            throw new BusinessException("获取产品详情失败: " + e.getMessage());
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 安全的单个产品获取，异常不抛出
     */
    private TzProductDTO getSingleProductSafely(String productId, boolean applyPricing) {
        try {
            return getProduct(productId, applyPricing);
        } catch (Exception e) {
            log.warn("单个产品获取失败，ID: {}, 错误: {}", productId, e.getMessage());
            return null;
        }
    }

    /**
     * 转换ProductInfoDTO列表为TzProductDTO列表并应用策略
     */
    private List<TzProductDTO> convertAndApplyStrategy(List<ProductInfoDTO> productInfoList, boolean applyPricing) {
        if (productInfoList == null || productInfoList.isEmpty()) {
            return Collections.emptyList();
        }

        // 先转换为TzProductDTO列表
        List<TzProductDTO> tzProducts = productInfoList.stream()
            .map(this::convertToTzProductDTO)
            .filter(Objects::nonNull)
            .toList();

        // 如果需要应用价格策略
        if (applyPricing && !tzProducts.isEmpty()) {
            return productStrategyService.processProduct(tzProducts);
        }

        return tzProducts;
    }

    /**
     * 将ProductInfoDTO转换为TzProductDTO
     */
    private TzProductDTO convertToTzProductDTO(ProductInfoDTO productInfo) {
        if (productInfo == null || productInfo.getId() == null) {
            return null;
        }

        try {
            // 尝试通过产品ID获取完整的TzProductDTO
            return productSyncService.getOrSyncProduct(productInfo.getId().toString());
        } catch (Exception e) {
            log.debug("转换产品数据失败，使用简化模式，产品ID: {}", productInfo.getId());

            // 创建简化的TzProductDTO
            TzProductDTO simpleProduct = new TzProductDTO();
            simpleProduct.setId(productInfo.getId());
            simpleProduct.setTitle(productInfo.getName());
//            simpleProduct.setPdcPlatformProductId(productInfo.getId());

            // 设置基础价格信息
            if (productInfo.getPrice() != null) {
                var simpleSku = new TzProductSkuDTO();
                simpleSku.setId(productInfo.getId());
                simpleSku.setPrice(productInfo.getPrice());
                simpleSku.setUsdPrice(productInfo.getUsdPrice());
                simpleProduct.setSkuList(List.of(simpleSku));
            }

            return simpleProduct;
        }
    }

    /**
     * 安全地应用价格策略，失败时降级处理
     */
    private TzProductDTO applyPricingWithFallback(TzProductDTO product, String productId) {
        if (product == null) {
            return null;
        }

        try {
            // 应用价格策略
            return productStrategyService.processProduct(product);
        } catch (Exception e) {
            log.warn("应用价格策略失败，产品ID: {}, 使用原始价格, 错误: {}", productId, e.getMessage());
            return product;
        }
    }

    /**
     * 安全地应用价格策略，失败时降级处理
     */
    private List<ProductInfoDTO> applyPricingWithFallback(List<ProductInfoDTO> productDTOS) {
        if (CollectionUtils.isEmpty(productDTOS)) {
            return null;
        }

        try {
            // 应用价格策略
            return productStrategyService.processProductInfoList(productDTOS);
        } catch (Exception e) {
            log.warn("应用价格策略失败，使用原始价格, 错误: {}", e.getMessage(), e);
            return productDTOS;
        }
    }

    /**
     * 轻量级价格策略应用
     * 仅对ProductInfoDTO列表应用价格策略，无额外数据转换
     */
    private List<ProductInfoDTO> applyLightweightPricing(List<ProductInfoDTO> productInfoList) {
        if (CollectionUtils.isEmpty(productInfoList)) {
            return productInfoList;
        }

        try {
            // 调用轻量级价格策略处理
            return productStrategyService.processProductInfoList(productInfoList);
        } catch (Exception e) {
            log.warn("轻量级价格策略应用失败，使用原始价格, 错误: {}", e.getMessage());
            return productInfoList;
        }
    }

    @Override
    public PageDTO<ProductInfoDTO> searchSimilarProducts(Long offerId, Integer pageIndex, Integer pageSize) {
        // 参数验证
        if (offerId == null) {
            throw new IllegalArgumentException("商品ID不能为空");
        }

        // 设置默认值
        int actualPageIndex = pageIndex != null ? Math.max(1, pageIndex) : 1;
        int actualPageSize = pageSize != null ? Math.min(Math.max(1, pageSize), 50) : 10;

        log.info("门面服务搜索相似商品: offerId={}, pageIndex={}, pageSize={}",
            offerId, actualPageIndex, actualPageSize);

        try {
            // 1. 使用PdcProductMappingRepository获取相似商品搜索结果
            // 注意：相似商品搜索是批量操作，保留直接调用Repository
            PageDTO<ProductInfoDTO> searchResult = pdcProductMappingRepository
                .searchSimilarProductsSync(offerId, LanguageEnum.EN, actualPageIndex, actualPageSize);

            if (searchResult == null || CollectionUtils.isEmpty(searchResult.getRecords())) {
                log.debug("相似商品搜索结果为空: offerId={}", offerId);
                return PageDTO.<ProductInfoDTO>builder()
                    .records(Collections.emptyList())
                    .pageIndex(actualPageIndex)
                    .pageSize(actualPageSize)
                    .total(0L)
                    .build();
            }

            // 2. 应用轻量级价格策略 - 仅对现有数据处理，无额外同步开销
            List<ProductInfoDTO> processedProducts = applyLightweightPricing(searchResult.getRecords());

            // 3. 构建返回结果
            PageDTO<ProductInfoDTO> result = PageDTO.<ProductInfoDTO>builder()
                .records(processedProducts)
                .pageIndex(searchResult.getPageIndex())
                .pageSize(searchResult.getPageSize())
                .total(searchResult.getTotal())
                .build();

            log.debug("门面服务相似商品搜索完成，结果数量: {}, 已应用价格策略", result.getRecords().size());
            return result;

        } catch (Exception e) {
            log.error("门面服务相似商品搜索异常: offerId={}", offerId, e);

            // 返回空结果，避免异常传播
            return PageDTO.<ProductInfoDTO>builder()
                .records(Collections.emptyList())
                .pageIndex(actualPageIndex)
                .pageSize(actualPageSize)
                .total(0L)
                .build();
        }
    }

    @Override
    public PageDTO<ProductInfoDTO> unifiedAggregateSearch(AggregateSearchReq request, Boolean useCache) {
        // 参数验证
        if (request == null) {
            throw new IllegalArgumentException("聚合搜索请求参数不能为空");
        }

        log.info("门面服务聚合搜索: searchType={}, keyword={}, imageId={}, imageUrl={}",
            request.getSearchType(), request.getKeyword(), request.getImageId(), request.getImageUrl());

        try {
            // 1. 使用PdcProductMappingRepository获取聚合搜索结果
            // 注意：聚合搜索是批量操作，保留直接调用Repository
            PageDTO<ProductInfoDTO> searchResult = pdcProductMappingRepository.unifiedAggregateSearchWithCache(request, !useCache);

            if (searchResult == null || CollectionUtils.isEmpty(searchResult.getRecords())) {
                log.debug("聚合搜索结果为空: searchType={}, keyword={}", request.getSearchType(), request.getKeyword());
                return PageDTO.<ProductInfoDTO>builder()
                    .records(Collections.emptyList())
                    .pageIndex(request.getPage() != null ? request.getPage() : 1)
                    .pageSize(request.getPageSize() != null ? request.getPageSize() : 20)
                    .total(0L)
                    .build();
            }

            // 2. 应用轻量级价格策略 - 仅对现有数据处理，无额外同步开销
            List<ProductInfoDTO> processedProducts = applyLightweightPricing(searchResult.getRecords());

            // 3. 构建返回结果
            PageDTO<ProductInfoDTO> result = PageDTO.<ProductInfoDTO>builder()
                .records(processedProducts)
                .pageIndex(searchResult.getPageIndex())
                .pageSize(searchResult.getPageSize())
                .total(searchResult.getTotal())
                .build();

            log.debug("门面服务聚合搜索完成，结果数量: {}, 已应用价格策略", result.getRecords().size());
            return result;

        } catch (Exception e) {
            log.error("门面服务聚合搜索异常: searchType={}, keyword={}", request.getSearchType(), request.getKeyword(), e);

            // 返回空结果，避免异常传播
            return PageDTO.<ProductInfoDTO>builder()
                .records(Collections.emptyList())
                .pageIndex(request.getPage() != null ? request.getPage() : 1)
                .pageSize(request.getPageSize() != null ? request.getPageSize() : 20)
                .total(0L)
                .build();
        }
    }
}
