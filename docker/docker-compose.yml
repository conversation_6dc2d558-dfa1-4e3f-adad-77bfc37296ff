version: '3'
services:
  mysql:
    image: mysql:8.0.33
    restart: always
    container_name: mysql
    ports:
      - '3308:3306'
    environment:
      TZ: Asia/Shanghai
      MYSQL_ROOT_PASSWORD: root
      # 初始化数据库（后续的初始化 SQL 会在这个库执行）
      MYSQL_DATABASE: fulfillmen_shop
      MYSQL_USER: fulfillmen
      MYSQL_PASSWORD: fulfillmen!@#123
    volumes:
      - ./mysql/conf/:/etc/mysql/conf.d/
      - ./mysql/data/:/var/lib/mysql/
    command:
      --default-authentication-plugin=mysql_native_password
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_general_ci
      --explicit_defaults_for_timestamp=true
      --lower_case_table_names=1
  redis:
    image: redis:7.2.3
    restart: always
    container_name: redis
    ports:
      - '6379:6379'
    environment:
      TZ: Asia/Shanghai
    volumes:
      - ./redis/conf/redis.conf:/usr/local/redis/config/redis.conf
      - ./redis/data/:/data/
      - ./redis/logs/:/logs/
    command: 'redis-server /usr/local/redis/config/redis.conf --appendonly yes --requirepass 123456'
